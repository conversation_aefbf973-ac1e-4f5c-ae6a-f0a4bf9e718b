import { Telegraf } from "telegraf";

const runtimeConfig = useRuntimeConfig();

interface reqSale {
  status: number;
  message: string;
  data: {
    order_id: string;
    customer_id: string;
    status: string;
    subtotal: string;
    delivery_fee: number;
    ccy: string;
    vat: string;
    totalAmount: number;
    payment_type: string;
    items: [];
  };
}

export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const channelID = -1002405560932;

  const bot = new Telegraf(runtimeConfig.telegramToken);

  try {
    // Construct the message
    let message = `🎉 New Order Alert! 🎉\n\n`;
    message += `Order ID: ${body.data.order_id}\n`;
    message += `Customer ID: ${body.data.customer_id}\n`;
    message += `Status: ${body.data.status}\n`;
    message += `Subtotal: ${body.data.subtotal} ${body.data.ccy}\n`;
    message += `Delivery Fee: ${body.data.delivery_fee} ${body.data.ccy}\n`;
    message += `VAT: ${body.data.vat} ${body.data.ccy}\n`;
    message += `Total Amount: ${body.data.totalAmount} ${body.data.ccy}\n`;
    message += `Payment Type: ${body.data.payment_type}\n`;
    message += `Items: (Details omitted for brevity)\n`;

    // Send the message to the Telegram channel
    await bot.telegram.sendMessage(channelID, message);
  } catch (error) {
    return {
      status: "error",
      message: error,
    };
  }

  return "Sale Has been notified";
});
