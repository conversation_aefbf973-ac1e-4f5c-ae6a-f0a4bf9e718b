import { Telegraf, Markup, Context } from "telegraf";
const runtimeConfig = useRuntimeConfig();

const bot = new Telegraf(runtimeConfig.telegramToken);
let botLaunched = false;

// export default defineEventHandler(async (event) => {
//   const body = await readBody(event);
//   console.warn("Recieved From Telegram Server:", body.message.chat);

// console.warn("This is telegram Token:", runtimeConfig.telegramToken);
const store_url = runtimeConfig.public.storeUrl;
const prodCatalog = runtimeConfig.public.prodCatalog;

async function miniappMenu(ctx: Context) {
  try {
    console.warn("init miniapp: ", store_url);
    if (!store_url) {
      await ctx.reply("sorry but the URL store not found");
      return;
    }

    const replyMarkup = Markup.inlineKeyboard([
      Markup.button.webApp("iCommerce", store_url ?? ""),
    ]);

    await ctx.reply(
      "Browing Store.... Please click on below button.",
      replyMarkup
    );
  } catch (error) {
    if (String(error.message).includes("BUTTON_TYPE_INVALID")) {
      console.error("init miniapp in group failed.", error);
      await ctx.reply("Sorry Miniapp is not available via group chat yet.");
      return;
    }
    await ctx.reply(`something error : ${error}`);
    console.error("error during init miniapp service", error);
  }
}

async function browseCatalog(ctx: Context) {
  const replyMarkup = Markup.inlineKeyboard([
    Markup.button.url("Browse iMenu", prodCatalog ?? ""),
  ]);

  console.warn("Product Catalog URL:", prodCatalog);

  await ctx.reply(
    "Please click on below button to browse the iMenu.",
    replyMarkup
  );
  console.warn(
    `${new Date().toISOString()} User ${ctx.from?.first_name} is browsing the catalog....`
  );
}

bot.command("start", async (ctx) => {
  console.warn("Telegram Bot is started by user: ", ctx.from.first_name);

  const name = ctx.from?.first_name + " " + ctx.from?.last_name;
  const replyMarkup = {
    inline_keyboard: [
      [
        {
          text: "Browse Store: iCommerce",
          callback_data: "store_init",
        },
        // Add more buttons to this row as needed
      ],
    ],
  };
  await ctx.reply("Hello, " + name + "! How can I help you?", {
    reply_markup: replyMarkup,
  });
});

bot.command("miniapp", async (ctx) => {
  await miniappMenu(ctx);
});

bot.command("browse", async (ctx) => {
  console.info(`User ${ctx.from.first_name} is browsing the catalog....`);
  await ctx.reply("Please wait. I'm looking for the iMenu for you....");
  await ctx.deleteMessage();
  try {
    await browseCatalog(ctx);
  } catch (error) {
    console.error("Error in browsing catalog:", error);
    await ctx.reply("Sorry, I couldn't find the products catalog.");
  }
});

bot.action("store_init", async (ctx) => {
  console.log("Got Init Button Clicked From:", ctx.from.first_name);
  await ctx.deleteMessage();

  const replyMarkup = Markup.inlineKeyboard([
    Markup.button.webApp("iCommerce", store_url ?? ""),
  ]);
  await ctx.reply(
    "Browing Store.... Please click on below button.",
    replyMarkup
  );
});

bot.on("text", async (ctx) => {
  console.warn(
    "Message Received: " + ctx.message.text + " | From:",
    ctx.from.first_name
  );

  await ctx.reply("You said: " + ctx.message.text);
});

bot.on("contact", async (ctx) => {
  const contact = ctx.message.contact.phone_number;

  console.warn("Got Contact:" + contact + "| From:", ctx.message.from.username);

  const data = {
    username: ctx.message.from.username,
    phone: ctx.message.contact.phone_number,
    chatid: ctx.message.chat.id,
    lang: ctx.message.from.language_code,
  };

  await ctx.reply(
    "Thank you, your contact is saved. Our Staff will contact you shortly. 🙏"
  );
  // await ctx.reply("Info Captured: " + JSON.stringify(data));
});

// if (!botLaunched) {
//   try {
//     bot.launch({
//       webhook: {
//         domain: store_url ?? "",
//         path: "/api/telegram-webhook",
//         port: 443,
//       },
//     });

//     console.warn("Bot is Lauched");
//     botLaunched = true;
//   } catch (error) {
//     console.error(error);
//   }
// }

// bot.launch();
// });

const webhookPath = "/api/telegram-webhook";
const webhookHandler = bot.webhookCallback(webhookPath);

export default defineEventHandler(async (event) => {
  await webhookHandler(event.node.req, event.node.res);
});
// bot.launch({ allowedUpdates: ["message", "callback_query"] });
