import { Telegraf } from "telegraf";

// Private variable Declaration
const runtimeConfig = useRuntimeConfig();
const store_url = process.env.STORE_URL;
const webhookPath = "/api/telegram-webhook";
const bot = new Telegraf(runtimeConfig.telegramToken);

let webhookInit = false;

export default fromNodeMiddleware(async (req, res, next) => {
  if (!webhookInit) {
    try {
      const webhookUrl = `${store_url}${webhookPath}`;

      bot.telegram
        .setWebhook(webhookUrl)
        .then(() => {
          console.log("Bot is initialized, Webhook set up completed");
          webhookInit = true;
        })
        .catch((error) => {
          console.error("Bot Set up failed. Caused: ", error);
        });
    } catch (error) {
      console.error("Failed to init telegram bot. Reason: ", error);
    }
    next();
  }
});
