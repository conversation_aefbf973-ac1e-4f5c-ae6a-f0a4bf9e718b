<template>
  <div class="min-h-screen bg-gray-50">
    <div class="container mx-auto px-4 py-6 max-w-4xl">
      <!-- Header -->
      <div class="flex items-center justify-between mb-6">
        <h1 class="text-2xl font-bold text-gray-800">
          My Orders ({{ orderData.length }})
        </h1>
        <NuxtLink
          to="/"
          class="flex items-center text-primary-600 hover:text-primary-800 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="h-5 w-5 mr-1"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fill-rule="evenodd"
              d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z"
              clip-rule="evenodd"
            />
          </svg>
          Back to Shop
        </NuxtLink>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="flex justify-center items-center py-12">
        <div
          class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"
        ></div>
      </div>

      <!-- Empty State -->
      <div
        v-else-if="orderData.length === 0"
        class="bg-white rounded-xl shadow-sm p-8 text-center"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          class="h-16 w-16 mx-auto text-gray-400"
          fill="none"
          viewBox="0 0 24 24"
          stroke="currentColor"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
            d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z"
          />
        </svg>
        <h3 class="mt-4 text-lg font-medium text-gray-900">No orders yet</h3>
        <p class="mt-1 text-gray-500">Your order history will appear here</p>
        <NuxtLink
          to="/"
          class="mt-6 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          Start Shopping
        </NuxtLink>
      </div>

      <!-- Order List -->
      <div v-else class="space-y-4">
        <div
          v-for="(order, index) in orderData"
          :key="index"
          @click="$router.push(`/history/${order.id}`)"
          class="bg-white rounded-xl shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200 cursor-pointer border border-gray-100"
        >
          <div class="p-5">
            <div class="flex justify-between items-start">
              <div>
                <h2 class="text-lg font-semibold text-gray-800">
                  Order #{{ order.id }}
                </h2>
                <p class="text-sm text-gray-500 mt-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-4 w-4 inline mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                  {{ formatDate(order.created_at) }}
                </p>
              </div>
              <div class="text-right">
                <p class="text-lg font-bold text-gray-900">
                  ${{ order.order_amount.toFixed(2) }}
                </p>
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium mt-1"
                  :class="statusClasses(order.order_status)"
                >
                  {{ formatStatus(order.order_status) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Order Footer -->
          <div class="bg-gray-50 px-5 py-3 flex justify-center items-center">
            <div class="flex space-x-2">
              <!-- <span class="inline-flex items-center text-sm text-gray-500">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  class="h-4 w-4 mr-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"
                  />
                </svg>
                {{ orderData.length || "0" }} Orders
              </span> -->
            </div>
            <button
              class="text-primary-600 hover:text-primary-800 text-sm font-medium transition-colors"
            >
              View Details
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import axios from "axios";
import { useAuth } from "~/composable/useAuth";

const orderData = ref([]);
const loading = ref(true);
const userstaus = ref();

// Use the auth composable
const auth = useAuth();

definePageMeta({
  layout: "custom",
});

// Format order date
const formatDate = (dateString: string) => {
  const options: Intl.DateTimeFormatOptions = {
    year: "numeric",
    month: "short",
    day: "numeric",
  };
  return new Date(dateString).toLocaleDateString(undefined, options);
};

// Format status text
const formatStatus = (status: string) => {
  const statusMap: Record<string, string> = {
    delivered: "Delivered",
    processing: "Processing",
    shipped: "Shipped",
    cancelled: "Cancelled",
  };
  return statusMap[status] || status;
};

// Status badge classes
const statusClasses = (status: string) => {
  return {
    "bg-green-100 text-green-800": status === "delivered",
    "bg-blue-100 text-blue-800": status === "processing",
    "bg-yellow-100 text-yellow-800": status === "shipped",
    "bg-red-100 text-red-800": status === "cancelled",
    "bg-gray-100 text-gray-800": ![
      "delivered",
      "processing",
      "shipped",
      "cancelled",
    ].includes(status),
  };
};

const getOrderHistory = async () => {
  // Initialize auth if not already initialized
  if (!auth.isAuthenticated.value) {
    await auth.initialize();
  }

  try {
    loading.value = true;
    const { apiUrl, apiPrefix } = useRuntimeConfig().public;
    const response = await axios.get(
      `${apiUrl}${apiPrefix}/customer/order/list?limit=100&offset=1`,
      {
        headers: {
          moduleId: "1",
          Authorization: auth.token.value ? `Bearer ${auth.token.value}` : "",
        },
      }
    );
    orderData.value = response.data.orders;
    console.log("orderData", orderData.value);
  } catch (error) {
    console.error("Error fetching order:", error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  getOrderHistory();
});
</script>

<style scoped>
/* Custom transition for order cards */
.order-enter-active,
.order-leave-active {
  transition: all 0.3s ease;
}

.order-enter-from,
.order-leave-to {
  opacity: 0;
  transform: translateY(10px);
}
</style>
