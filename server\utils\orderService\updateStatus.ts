import { Telegraf } from "telegraf";
const runtimeConfig = useRuntimeConfig();

export async function updateStatus(
  chatid: number,
  order_id: number,
  status: string
) {
  const get_status = (status: string) => {
    switch (status.toLowerCase()) {
      case "pending":
        return `Your Order #${order_id} has been placed. \nAwaiting confirmation from the store.`;
      case "confirmed":
        return `👌 Great news, Your order #${order_id} has been accepted. \nPlease wait for the store to prepare your order.`;
      case "processing":
        return `🎁 Your order #${order_id} is being processed.`;
      case "handover":
        return `🚀 Your order #${order_id} is being handed over to our delivery man.`;
      case "picked_up":
        return `🛵 Your order #${order_id} is on their way.\nPlease for their contact. 🙏🙏`;
      case "delivered":
        return `🏠 We notice your order ${order_id} has been delivered. \nThank you for using our service 😍😍`;
      case "canceled":
        return "Cancelled";
      case "refunded":
        return "Refunded";
      default:
        return "Unknown";
    }
  };
  const bot = new Telegraf(runtimeConfig.telegramToken);

  if (!status) {
    return "Metadata not found";
  }
  const sendMesage = await bot.telegram.sendMessage(
    chatid,
    `
      ${get_status(status)}
      `,
    { parse_mode: "Markdown" }
  );
  return sendMesage;
}
