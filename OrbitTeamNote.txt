PENDING: 
 - Confirm Order (refactor)
 - Add delivery and payment method in confirm Order 
 - Swipe suggested product component on Prod Detail page. (Done)
 - Order History Detail (Done)

Done: 
 - Product Card is okay.
 - Added new Order History
 - New Repo plugin for Icons (npm install --save-dev @iconify/vue)



 ---------------------
 Suggestion:
    - Authenticate using API Key to block API public (Less secure)
    - Using Telegram Hash (Useful: 1. Block incoming source beside miniapp, use it to authenicate user) ++ Bong justin to check
        + Through Header (but suggest body instead)
        ++ Hash -> Base64
    - Setup CORS (Me)

---------
    - Get Authorize JWT during get User

--------- 25/02/2025
    - Get Order Detail recipe from Merchant Portal (Done)
    - Create table structure for order (Done)
    -> In need for Login authenicate first (Done)
    - Push Message and forward the recripe to Telegram via Chat ID (Done)

------ 
    - Update telegram status from order (Notify) -> Done
    - Disable Zoom and Scroll (meta size) -> Done but bug found
    - Resize on display screen -> Done

-------- 3/4/2025
    - Fix Disable Zoom behavior
    - Implement request location button
        -> Query address on demand via Google Map