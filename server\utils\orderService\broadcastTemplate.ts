// const runtimeConfig = useRuntimeConfig();
// import { PrismaClient } from "@prisma/client";

// const prisma = new PrismaClient();

export default async function BroadcastTemplate(
  title: string | "Message Title",
  message: string | "message unknown",
  image: string | "unknown"
) {
  const broadcast = `
    title: ***title*** ${title}
    ------ 
    (message: ${message})
    `;

  const url = image;

  return {
    broadcast,
    url,
  };
}
