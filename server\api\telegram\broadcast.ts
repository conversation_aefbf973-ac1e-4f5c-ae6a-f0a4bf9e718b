const runtimeConfig = useRuntimeConfig();
import BroadcastTemplate from "~/server/utils/orderService/broadcastTemplate";
import { PrismaClient } from "@prisma/client";
import { Telegraf } from "telegraf";

const prisma = new PrismaClient();

export default defineEventHandler(async (event) => {
  const { title, message, image, groupid } = await readBody(event);

  let chatIds: number[] = [];

  if (groupid) {
    chatIds = [Number(groupid)];
  } else {
    const users = await prisma.customer.findMany();
    chatIds = users.map((user) => Number(user.chatid));
  }

  const bot = new Telegraf(runtimeConfig.telegramToken);

  for (const chatId of chatIds) {
    try {
      await bot.telegram.sendPhoto(
        chatId,
        image, // image URL or Telegram file_id
        {
          caption: `Title ${title}, 
          \n${message}`,
        }
      );

      // Optional: small delay to respect Telegram limits
      await new Promise((resolve) => setTimeout(resolve, 50));
    } catch (error) {
      console.error(
        `Failed to send message to chatId ${chatId}:`,
        error.message
      );
    }
  }
});
