import { z } from "zod";
import { Telegraf } from "telegraf";

// Constants and Types
const TELEGRAM_MESSAGE_TYPES = ["text", "image", "video", "file"] as const;
type TelegramMessageType = (typeof TELEGRAM_MESSAGE_TYPES)[number];

// Zod Schema
const requestBodySchema = z.object({
  chatid: z.string().min(1, "Chat ID cannot be empty"),
  type: z.enum(TELEGRAM_MESSAGE_TYPES, {
    errorMap: () => ({ message: "Invalid message type" }),
  }),
  data: z.string().min(1, "Data cannot be empty"),
});

// Telegram Bot Setup
const runtimeConfig = useRuntimeConfig();
const bot = new Telegraf(runtimeConfig.telegramToken);

// Helper Functions
const validateOrigin = (
  hostname: string | undefined,
  allowedHosts: string[]
) => {
  if (!hostname || !allowedHosts.includes(hostname)) {
    throw createError({
      statusCode: 403,
      message: "Request Origin Forbidden",
      stack: `${new Date().toISOString()} | Error Origin Request Header -> ${hostname}`,
    });
  }
};

const sendTelegramMessage = async (
  chatId: string,
  type: TelegramMessageType,
  data: string
) => {
  try {
    switch (type) {
      case "text":
        return await bot.telegram.sendMessage(chatId, data);
      case "image":
        return await bot.telegram.sendPhoto(chatId, data);
      case "video":
        return await bot.telegram.sendVideo(chatId, data);
      case "file":
        return await bot.telegram.sendDocument(chatId, data);
    }
  } catch (error) {
    console.error(`Failed to send ${type} message:`, error);
    throw createError({
      statusCode: 500,
      message: `Failed to send ${type} message to Telegram`,
    });
  }
};

// Main Handler
export default defineEventHandler(async (event) => {
  try {
    // Origin Validation
    validateOrigin(
      event.node.req.headers.host,
      runtimeConfig.public.allowedHosts as string[]
    );

    // Request Body Validation
    const requestBody = await readBody(event);
    const result = await requestBodySchema.safeParseAsync(requestBody);

    if (!result.success) {
      throw createError({
        statusCode: 400,
        message: "Validation Error",
        cause: result.error.flatten(),
      });
    }

    // Send Message
    const { chatid, type, data } = result.data;
    await sendTelegramMessage(chatid, type, data);

    return { success: true };
  } catch (error: any) {
    console.error(
      `${new Date().toISOString()} | Error in /telegram/chat/send ->`,
      error
    );

    if (error.statusCode) throw error;

    throw createError({
      statusCode: error instanceof z.ZodError ? 400 : 500,
      message:
        error instanceof z.ZodError
          ? "Validation Error"
          : "Internal Server Error",
      cause: error instanceof z.ZodError ? error.flatten() : error,
    });
  }
});
