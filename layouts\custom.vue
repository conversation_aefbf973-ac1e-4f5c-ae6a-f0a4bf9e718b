<template>
  <Header />

 <!-- <div class="container w-full mx-auto mb-10 flex justify-center"> -->
 <div>
    <transition name="fade" mode="out-in">
      <slot />
    </transition>
  </div>

  <!-- <BottomBar /></div> -->
</template>

<script setup>

const router = useRouter()
</script>

<style>
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to

/* .fade-leave-active in <2.1.8 */
  {
  opacity: 0;
}
</style>