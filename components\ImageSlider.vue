<template>
  <div class="relative w-full overflow-hidden" @touchstart="handleTouchStart" @touchend="handleTouchEnd">
    <div class="flex transition-transform duration-300" :style="{ transform: `translateX(-${currentIndex * 100}%)` }">
      <div v-for="(image, index) in images.flat()" :key="index" class="flex-shrink-0 w-full">
        <!-- <NuxtImg :src="resolveImg(image)" alt="Carousel Image" class="w-full h-60 object-contain" /> -->
        <NuxtImg :src="useResolveImage(image)" alt="Carousel Image" class="w-full h-60 object-contain" />
      </div>
    </div>

    <!-- Indicators -->
    <div class="absolute bottom-3 left-1/2 -translate-x-1/2 flex items-center gap-2">
      <button v-for="(image, index) in images" :key="index" @click="goToSlide(index)"
        class="h-2.5 transition-all duration-300 rounded-full" :class="{
          'w-6 bg-blue-600 shadow-md': currentIndex === index,
          'w-2 bg-gray-300': currentIndex !== index,
        }"></button>
    </div>

    <!-- Previous and Next Buttons -->
    <button @click="prevSlide" class="absolute -left-4 top-1/2 transform -translate-y-1/2 rounded-full p-2 shadow">
      <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" class="text-gray-700">
        <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="m15 6l-6 6l6 6" />
      </svg>
    </button>
    <button @click="nextSlide" class="absolute -right-4 top-1/2 transform -translate-y-1/2 rounded-full p-2 shadow">
      <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" class="text-gray-600">
        <path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="m9 6l6 6l-6 6" />
      </svg>
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps } from "vue";
import { useResolveImage } from "~/composable/useResolveImage";

const resolveImg = useResolveImage;

// Define props
const props = defineProps<{
  imagesProps: [];
}>();

if (props) {
  console.log("Props Recieved>>>>>>>>>", props.imagesProps);
}
const currentIndex = ref(0);
const images = ref([props.imagesProps]);
console.log("Images Props: >>>>>>>>>>>", images.value);
console.log("Images Props LENGTH: >>>>>>>>>>>", images.value.length);

if (images.value.length >= 1) {
  images.value.push(props.imagesProps);
  images.value.push(props.imagesProps);
}

// Touch handling variables
const touchStartX = ref(0);
const touchEndX = ref(0);

// Navigation functions
const nextSlide = () => {
  currentIndex.value = (currentIndex.value + 1) % images.value.length;
};

const prevSlide = () => {
  currentIndex.value =
    (currentIndex.value - 1 + images.value.length) % images.value.length;
};

const goToSlide = (index: number) => {
  currentIndex.value = index;
};

// Touch event handlers
const handleTouchStart = (event: TouchEvent) => {
  touchStartX.value = event.changedTouches[0].screenX;
};

const handleTouchEnd = (event: TouchEvent) => {
  touchEndX.value = event.changedTouches[0].screenX;
  handleSwipe();
};

const handleSwipe = () => {
  if (touchEndX.value < touchStartX.value) {
    nextSlide();
  } else if (touchEndX.value > touchStartX.value) {
    prevSlide();
  }
};
</script>

<style scoped>
/* Optional: Add some transition effects */
</style>
