import { PrismaClient } from "@prisma/client";
import { loginService } from "#imports";

const prisma = new PrismaClient();

export default defineEventHandler(async (event) => {
  try {
    // Get the request body
    const body = await readBody(event);
    console.log('Request body:', body);

    const { cartData, token, chatId } = body;

    if (!cartData) {
      console.error('Cart data is missing');
      return {
        status: false,
        message: "Cart data is required"
      };
    }

    // Get the most recent token from the database using chatId
    let currentToken = token;

    if (chatId) {
      try {
        // Get a fresh token from the login service
        console.log('Getting fresh token for chatId:', chatId);
        const loginRes = await loginService(String(chatId));

        if (loginRes.status === 200 && loginRes.data) {
          currentToken = loginRes.data;
          console.log('Using fresh token from login service');
        }
      } catch (tokenError) {
        console.error('Error getting fresh token:', tokenError);
        // Continue with the provided token if refresh fails
      }
    }

    if (!currentToken) {
      console.error('Token is missing');
      return {
        status: false,
        message: "Authorization token is required. Please log in to continue."
      };
    }

    console.log('Using token for API call:', currentToken);

    // Get runtime config
    const config = useRuntimeConfig();
    const { apiUrl, apiPrefix, moduleId, storeId, zoneId } = config.public;

    console.log('Making API call to:', `${apiUrl}${apiPrefix}/customer/cart/add`);
    console.log('With cart data:', cartData);

    // Make the API call to the external service using $fetch
    // Log the full URL and headers for debugging
    console.log('Full API URL:', `${apiUrl}${apiPrefix}/customer/cart/add`);
    console.log('Headers:', {
      moduleId: moduleId,
      zoneId: zoneId,
      storeId: storeId,
      Authorization: `Bearer ${currentToken}`
    });

    // Try to get the exact format of the token from the database
    let dbToken = currentToken;
    if (chatId) {
      try {
        const customer = await prisma.customer.findUnique({
          where: {
            chatid: String(chatId),
          },
          select: {
            user_token: true,
          },
        });

        if (customer?.user_token) {
          dbToken = customer.user_token;
          console.log('Using token directly from database');
        }
      } catch (dbError) {
        console.error('Error getting token from database:', dbError);
      }
    }

    const response = await $fetch(`${apiUrl}${apiPrefix}/customer/cart/add`, {
      method: 'POST',
      body: cartData,
      headers: {
        moduleId: moduleId,
        zoneId: zoneId,
        storeId: storeId,
        Authorization: `Bearer ${dbToken}`
      }
    });

    console.log('API response:', response);

    // Return the response data
    return {
      status: true,
      message: "Item added to cart successfully",
      data: response
    };
  } catch (error) {
    // Handle errors
    console.error('Error adding item to cart:', error);

    // More detailed error logging
    if (error.response) {
      console.error('Error response:', error.response);
    }

    return {
      status: false,
      message: error.data?.message || "Error adding item to cart",
      error: error.message || String(error)
    };
  }
});