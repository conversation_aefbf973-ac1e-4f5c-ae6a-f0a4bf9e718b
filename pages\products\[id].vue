<template>
  <section class="mb-10">
    <!--    <h1>Debug {{debugUser.user.id}}</h1>-->
    <div class="container mx-auto">
      <div class="lg:col-gap-12 xl:col-gap-16 mt-8 grid grid-cols-1 gap-12 lg:mt-12 lg:grid-cols-5 lg:gap-16">
        <!-- Display Selected Image -->
        <div v-if="selectedColor" class="mt-4 flex justify-center">
          <div class="image-container">
            <img :src="`${urlImg}/product/${selectedImage}`" alt="Selected Color Image" class="product-image" />
          </div>
        </div>

        <div v-else>
          <ImageSlider :imagesProps="getProductImg" v-if="getProductImg.length > 0" />
        </div>

        <div class="lg:col-span-2 lg:row-span-2 lg:row-end-2">
          <h5 class="text-xl font-light text-gray-900">{{ product.name }}</h5>
          <div class="flex mt-2 justify-between items-center">
            <h1 class="text-2xl font-semibold">
              <span v-if="product.discount !== 0" class="text-xl text-center font-semibold">
                <span class="text-sm text-center font-semibold mr-1" :class="{
                  'line-through text-gray-500': product.discount !== 0,
                }">
                  ${{ product.price }}
                </span>
                ${{
                  (product.discount_type === "percent"
                    ? product.price - (product.price * product.discount) / 100
                    : product.price - product.discount
                  ).toFixed(2)
                }}
              </span>

              <span v-else class="text-xl text-center font-semibold">
                ${{ product.price }}
              </span>
            </h1>
            <div class="quantity-container flex items-center">
              <button @click="decreaseQuantity" class="btn btn-secondary btn-square">
                <Icon icon="si:remove-fill" width="24" height="24" />
              </button>
              <input type="text" inputmode="numeric" id="quantity" v-model="quantity" min="1" @input="logQuantity"
                class="quantity-input mx-2" />
              <button @click="increaseQuantity" class="btn btn-secondary btn-square">
                <Icon icon="si:add-fill" width="24" height="24" />
              </button>
            </div>
          </div>
          <!-- Collapse -->
          <div class="mt-4">
            <div tabindex="0" class="w-full collapse collapse-arrow bg-base-200" :class="{ 'collapse-close': isClose }"
              @click="toggleCollapse">
              <div class="collapse-title text-base font-medium">
                Description
              </div>
              <div class="collapse-content">
                <p class="text-base text-gray-500">{{ product.description }}</p>
              </div>
            </div>
          </div>

          <div class="form-control">
            <div class="mt-3 select-none flex-wrap items-center grid grid-cols-3 gap-1">
              <h2 class="col-span-3 text-base font-bold text-gray-900">
                {{ sizeOptions.title }}
              </h2>
              <div v-for="(option, index) in sizeOptions.options" :key="index" class="w-full">
                <label>
                  <input type="radio" name="size" :value="option" v-model="selectedSize" class="peer sr-only" />
                  <p
                    class="w-full min-w-[40px] border border-red-200 rounded-md p-2 text-center cursor-pointer transition-all duration-200 hover:border-primary hover:bg-primary/10 hover:shadow-sm peer-checked:border-primary peer-checked:bg-primary/10 peer-checked:text-primary peer-checked:font-medium peer-checked:ring-1 peer-checked:ring-primary">
                    {{ option }}
                  </p>
                </label>
              </div>
            </div>

            <div class="form-control">
              <div class="mt-3 select-none flex-wrap items-center grid grid-cols-3 gap-1">
                <h2 v-show="product.colors > 0" class="col-span-3 text-base font-bold text-gray-900">
                  Choose Colors
                </h2>
                <div v-for="(option, index) in selectedSizes" :key="index" class="w-full">
                  <label>
                    <input type="radio" name="size" :value="option" v-model="selectedColor" class="peer sr-only" />
                    <p
                      class="w-full min-w-[40px] border border-red-200 rounded-md p-2 text-center cursor-pointer transition-all duration-200 hover:border-primary hover:bg-primary/10 hover:shadow-sm peer-checked:border-primary peer-checked:bg-primary/10 peer-checked:text-primary peer-checked:font-medium peer-checked:ring-1 peer-checked:ring-primary">
                      {{ option.type }} (${{ option.price }})
                    </p>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div class="button-container w-full flex items-center gap-1">
            <button @click="buyNow" class="btn btn-primary">Buy Now</button>
            <button @click="addItemToCart" class="btn btn-secondary">
              Add to Cart
            </button>
          </div>
        </div>
        <Prorecommended />
        <PlaceOrderForm :id="id" :total="(product.price - (product.price * product.discount) / 100) *
          quantity +
          shipping
          " :shipping="shipping || 0" :price="product.price || 0" :product="product.name || ''"
          :discount="String(product.discount || 0)" :image="product.image_full_url || ''" :quantity="quantity || 0"
          :name="''" :phone="''" :variation="selectedColor && selectedColor.type ? selectedColor.type : 'N/A'
            " />
      </div>
    </div>
  </section>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import Swal from "sweetalert2";
import { useRoute } from "vue-router";
import { init, retrieveLaunchParams } from "@telegram-apps/sdk";
import { isTMA } from "@telegram-apps/sdk";
import { useResolveImage } from "~/composable/useResolveImage";

const { id } = useRoute().params;
const shipping = 2;
const isClose = ref(true);
const product = ref({});
const colorOptions = ref({ title: "", options: [] });
const sizeOptions = ref({ title: "", options: [] });
const variationOption = ref({ variations: [] });
const debugUser = ref();
//show only one
// const getProductImg = computed(() => {
//   console.log("product.value", product.value.images_full_url);
//   if (
//     product.value.images_full_url &&
//     product.value.images_full_url.length > 0
//   ) {
//     return product.value.images_full_url;
//   }

//   // Fallback if product has image_full_url but no images array
//   if (product.value.images_full_url && product.value.images_full_url) {
//     return [useResolveImage(product.value.images_full_url)];
//   }

//   return [];
// });
//product image all
const getProductImg = computed(() => {
  console.log("product.value", product.value.images_full_url);
  if (product.value.images_full_url && product.value.images_full_url.length > 0) {
    return product.value.images_full_url;
  }

  // Fallback if product has image_full_url but no images array
  if (product.value.images_full_url && product.value.images_full_url) {
    return [useResolveImage(product.value.images_full_url)];
  }

  return [];
});
const quantity = ref(1);
const selectedColor = ref(null);
const selectedSize = ref(null);
const images = ref([]);
const phone = ref("");
const name = ref("");
const getUserToken = ref(null);
const getUserChatId = ref(null);
definePageMeta({
  layout: "custom",
});
const { urlImg } = useRuntimeConfig().public;
const imageUrls = computed(() => {
  if (selectedImage.value) {
    const url = `${urlImg}/product/${selectedImage.value}`;
    return [url];
  }
  return []; // Return an empty array if no image is selected
});

const selectedImage = computed(() => {
  if (selectedColor.value?.type && images.value.length > 0) {
    // Extract the part before the hyphen from selectedColor.value.type
    const selectedColorPrefix = selectedColor.value.type.split("-")[0];

    // Find the matching image based on the extracted color
    const matchedImage = images.value.find(
      (img) => img.color.toLowerCase() === selectedColorPrefix.toLowerCase()
    );

    return matchedImage ? matchedImage.img : null;
  }
  return "001";
});

const selectedSizes = computed(() => {
  if (selectedSize.value && variationOption.value.length > 0) {
    const matchedImage = variationOption.value.filter((type) => {
      const removeSub = type.type.split("-").pop();
      return removeSub === selectedSize.value;
    });
    return matchedImage;
  }
});

function toggleCollapse() {
  isClose.value = !isClose.value;
}
function logQuantity() {
  // Handle quantity input changes if needed
}
function increaseQuantity() {
  quantity.value++;
}

function decreaseQuantity() {
  if (quantity.value > 1) {
    quantity.value--;
  }
}

async function addItemToCart() {
  // Check if user is logged in
  if (process.client) {
    const storedToken = localStorage.getItem("userToken");
    console.log("Stored token for addItemToCart:", storedToken);

    if (storedToken) {
      getUserToken.value = storedToken;
      console.log("Set token from localStorage:", getUserToken.value);
    }
  }

  // If not logged in, attempt automatic login
  if (!getUserToken.value) {
    console.log("No token found, attempting login for Add to Cart");

    // Show a loading indicator
    Swal.fire({
      title: "Logging in...",
      text: "Please wait while we log you in automatically",
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    // Re-initialize Telegram SDK and try login
    if (isTMA()) {
      try {
        await init();
        console.log("Re-initialized Telegram SDK");
      } catch (e) {
        console.error("Error re-initializing SDK:", e);
      }
    }

    const loginSuccess = await login();
    Swal.close(); // Close the loading indicator

    if (!loginSuccess) {
      // If automatic login failed, show a message
      await Swal.fire({
        icon: "error",
        title: "Login Required",
        text: "Please open this page in Telegram Mini App to use this feature.",
      });
      return;
    }
  }

  const cartData = {
    item_id: id,
    model: "Item",
    price:
      selectedColor.value?.price !== undefined
        ? selectedColor.value.price
        : product.value?.price,
    quantity: quantity.value,
    variation: JSON.stringify(
      selectedColor.value &&
        selectedColor.value.type !== undefined &&
        selectedColor.value.type !== "N/A"
        ? [{ type: selectedColor.value.type.replace(/\s+/g, "") }]
        : []
    ),
  };

  try {
    // Log the token being used
    console.log("Using token for cart API:", getUserToken.value);

    // Call our server API endpoint
    const response = await $fetch("/api/cart/incart", {
      method: "POST",
      body: {
        cartData,
        token: getUserToken.value,
        chatId: getUserChatId.value,
      },
    });

    console.log("Cart API response:", response);

    if (response.status) {
      await Swal.fire({
        icon: "success",
        title: "Success!",
        text: "Item added to cart successfully.",
        confirmButtonText: "OK",
      });
    } else {
      throw new Error(response.message);
    }
  } catch (error) {
    await Swal.fire({
      icon: "error",
      title: "Error",
      text: error.message || "Item already exists",
      confirmButtonText: "OK",
    });
  }
}

async function buyNow() {
  // Check if user is logged in
  if (process.client) {
    const storedToken = localStorage.getItem("userToken");
    console.log("Stored token for buyNow:", storedToken);

    if (storedToken) {
      getUserToken.value = storedToken;
      console.log("Set token from localStorage:", getUserToken.value);
    }
  }

  // If not logged in, attempt automatic login
  if (!getUserToken.value) {
    console.log("No token found, attempting login for Buy Now");

    // Show a loading indicator
    Swal.fire({
      title: "Logging in...",
      text: "Please wait while we log you in automatically",
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      },
    });

    // Re-initialize Telegram SDK and try login
    if (isTMA()) {
      try {
        await init();
        console.log("Re-initialized Telegram SDK");
      } catch (e) {
        console.error("Error re-initializing SDK:", e);
      }
    }

    const loginSuccess = await login();
    Swal.close(); // Close the loading indicator

    if (!loginSuccess) {
      // If automatic login failed, show a message
      await Swal.fire({
        icon: "error",
        title: "Login Required",
        text: "Please open this page in Telegram Mini App to use this feature.",
      });
      return;
    }
  }

  const cartData = {
    item_id: id,
    model: "Item",
    price:
      selectedColor.value?.price !== undefined
        ? selectedColor.value.price
        : product.value?.price,
    quantity: quantity.value,
    variation: JSON.stringify(
      selectedColor.value &&
        selectedColor.value.type !== undefined &&
        selectedColor.value.type !== "N/A"
        ? [{ type: selectedColor.value.type.replace(/\s+/g, "") }]
        : []
    ),
  };

  try {
    // Log the token being used
    console.log("Using token for buyNow API:", getUserToken.value);

    // Call our server API endpoint
    const response = await $fetch("/api/cart/incart", {
      method: "POST",
      body: {
        cartData,
        token: getUserToken.value,
        chatId: getUserChatId.value,
      },
    });

    console.log("BuyNow API response:", response);

    if (response.status) {
      // Add debug logging
      console.log("Product data:", product.value);
      console.log("Quantity:", quantity.value);
      console.log("Shipping:", shipping.value);

      // Calculate total price with discount and shipping with safety checks
      let totalPrice = 0;
      try {
        const productPrice = product.value?.price || 0;
        const productDiscount = product.value?.discount || 0;
        const qty = quantity.value || 1;
        const ship = shipping.value || 0;

        totalPrice = (productPrice - (productPrice * productDiscount / 100)) * qty + ship;
        console.log("Calculated total price:", totalPrice);
      } catch (e) {
        console.error("Error calculating price:", e);
        totalPrice = 200; // Default fallback price
      }

      // Navigate to confirm page instead of checkout
      const orderData = {
        order_amount: String(totalPrice),
        price: String(product.value?.price || 0),
        product: product.value?.name || "Product",
        shipping: String(shipping.value || 0),
        image: product.value?.image_full_url || "",
        quantity: String(quantity.value || 1),
        discount: String(product.value?.discount || 0),
        payment_method: "cash_on_delivery",
        order_type: "delivery",
        store_id: "13",
        distance: "2",
        address: "",
        name: "",
        phone: "",
        token: getUserToken.value || "",
        chatid: getUserChatId.value || "",
        longitude: "0",
        latitude: "0",
      };

      console.log("Order data being sent to confirm page:", orderData);

      navigateTo({
        name: "confirm-id",
        params: { id: id },
        query: orderData,
      });
    } else {
      throw new Error(response.message);
    }
  } catch (error) {
    await Swal.fire({
      icon: "error",
      title: "Error",
      text: error.message || "Error adding item to cart",
      confirmButtonText: "OK",
    });
  }
}

async function fetchUserToken(tgWebAppData) {
  try {
    console.log("Fetching user token for ID:", tgWebAppData.user?.id);

    const { data } = await useFetch("/api/telegram/customer/getToken", {
      method: "POST",
      body: {
        id: tgWebAppData.user?.id,
        auth_date: tgWebAppData.auth_date,
        hash: tgWebAppData.hash,
      },
    });

    if (data.value?.data) {
      const token = data.value.data;
      console.log("Received token from getToken API:", token);

      // Store in reactive variables
      getUserToken.value = token;
      phone.value = data.value?.profile?.phone || "";
      name.value = data.value?.profile?.name || "";
      getUserChatId.value = tgWebAppData.user?.id;

      // Store token in local storage
      if (process.client) {
        localStorage.setItem("userToken", token);
        localStorage.setItem("userChatId", tgWebAppData.user?.id);
        console.log("Token stored in localStorage from fetchUserToken");

        // Verify token was stored correctly
        const storedToken = localStorage.getItem("userToken");
        console.log("Verified token in localStorage:", storedToken);
      }

      return token;
    } else {
      console.warn("No token received from getToken API");
      return null;
    }
  } catch (error) {
    console.error("Error fetching user token:", error);
    return null;
  }
}

async function login() {
  try {
    // Check if we're in Telegram environment
    if (!isTMA()) {
      await Swal.fire({
        icon: "warning",
        title: "Telegram Login Required",
        text: "This feature requires Telegram Mini App environment.",
      });
      return false;
    }

    // Re-initialize Telegram SDK to ensure we have the latest data
    await init();
    const launchParams = retrieveLaunchParams().tgWebAppData;
    debugUser.value = launchParams;

    // Get user data directly from launch params
    const user = launchParams.user;
    const chatId = user?.id;

    if (!chatId) {
      console.error("No Telegram user ID available", launchParams);
      await Swal.fire({
        icon: "error",
        title: "Login Failed",
        text: "Could not get Telegram user ID.",
      });
      return false;
    }

    console.log("Found Telegram user ID:", chatId);

    // Call login API
    const response = await $fetch("/api/telegram/customer/login", {
      method: "POST",
      body: {
        id: chatId,
      },
    });

    console.log("Login response:", response);

    if (response.status === 200 && response.data) {
      // Store token in local storage and in the reactive variable
      const token = response.data;
      console.log("Storing token from login API:", token);

      // Store token in reactive variable
      getUserToken.value = token;
      getUserChatId.value = chatId;

      // Store token in localStorage
      if (process.client) {
        localStorage.setItem("userToken", token);
        localStorage.setItem("userChatId", chatId);

        // Verify token was stored correctly
        const storedToken = localStorage.getItem("userToken");
        console.log("Verified stored token in localStorage:", storedToken);
      }

      // Success notification (brief)
      await Swal.fire({
        icon: "success",
        title: "Login Successful",
        timer: 1000,
        showConfirmButton: false,
      });

      return true;
    } else {
      throw new Error(response.message || "Login failed");
    }
  } catch (error) {
    console.error("Login error:", error);
    await Swal.fire({
      icon: "error",
      title: "Login Failed",
      text: error.message || "Could not log in automatically.",
    });
    return false;
  }
}

// Fetch product data from the API
onMounted(async () => {
  // Check if user token is stored in local storage
  if (process.client) {
    const storedToken = localStorage.getItem("userToken");
    const storedChatId = localStorage.getItem("userChatId");

    if (storedToken) {
      getUserToken.value = storedToken;
    }

    if (storedChatId) {
      getUserChatId.value = storedChatId;
    }
  }
  try {
    const response = await $fetch(`/api/products/${id}`, {
      method: "GET",
    });
    if (!response.status) {
      alert("Error Fetch Data" + response.message);
      return;
    }

    product.value = await response.data;
    if (response.data.colors) {
      const variation = JSON.parse(response.data.colors);
      colorOptions.value.options = variation.map((color) => color);
      return;
    }

    if (response.data.images) {
      images.value = Array.isArray(response.data.images)
        ? response.data.images
        : [];
    }

    const choiceOptions = response.data.choice_options || [];
    choiceOptions.forEach((choice) => {
      if (choice.title === "Color") {
        colorOptions.value = choice;
      } else if (choice.title === "Size") {
        sizeOptions.value = choice;
      }
    });

    variationOption.value = await response.data.variations;
  } catch (error) {
    console.error("Error fetching product data:", error);
  }

  // Telegram Mini App authentication
  if (isTMA()) {
    try {
      console.log("Detected Telegram Mini App environment");
      await init();
      const launchParams = retrieveLaunchParams();
      console.log("Launch params:", launchParams);

      // If we have a user, try to get the token
      if (launchParams.user) {
        console.log("User found in launch params:", launchParams.user);

        // First try to get token from the getToken endpoint
        // This ensures we get the most up-to-date token from the database
        const token = await fetchUserToken(launchParams);
        console.log("Token from fetchUserToken:", token);

        // If no token was found, try login
        if (!token) {
          console.log("No token from fetchUserToken, trying login");
          const loginSuccess = await login();
          console.log("Automatic login result:", loginSuccess);
        }

        // Verify we have a token now
        if (getUserToken.value) {
          console.log("Successfully obtained token:", getUserToken.value);
        } else {
          console.warn("Failed to obtain token after multiple attempts");
        }
      } else {
        console.warn("No user found in Telegram launch params");
      }
    } catch (error) {
      console.error("Error initializing Telegram Mini App:", error);
    }
  } else {
    console.log("Not in Telegram Mini App environment");
  }
});
</script>

<style scoped>
.quantity-btn {
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 0.375rem;
  padding: 0.5rem 1rem;
  font-size: 1.25rem;
  cursor: pointer;
  transition: background-color 0.3s;
}

.quantity-btn:hover {
  background-color: #357abd;
}

.quantity-input {
  width: 60px;
  text-align: center;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  padding: 0.5rem;
  font-size: 1rem;
}

.image-container {
  border: 4px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
}

.image-container:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.product-image {
  width: 200px;
  height: 200px;
  object-fit: cover;
  display: block;
}

.button-container {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-top: 20px;
}

.btn-buy-now,
.btn-add-to-cart {
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition:
    background-color 0.3s ease,
    transform 0.3s ease;
}

.btn-buy-now {
  background-color: #f58300;
  color: #ffffff;
}

.btn-buy-now:hover {
  background-color: #ff6b81;
  transform: translateY(-2px);
}

.btn-add-to-cart {
  background-color: #1455ac;
  color: #ffffff;
}

.btn-add-to-cart:hover {
  background-color: #1455ac;
  transform: translateY(-2px);
}
</style>
