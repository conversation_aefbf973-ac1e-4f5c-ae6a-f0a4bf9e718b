<template>
  <!-- This component handles Telegram user authentication silently -->
</template>

<script lang="ts" setup>
import { useAuth } from '~/composable/useAuth';

// Get the auth composable
const auth = useAuth();
const debugStatus = ref({});
const userStatus = ref();

// Provide the auth state to the rest of the application
// This makes getUserToken and getUserChatId available to other components
// for backward compatibility
const getUserToken = computed(() => auth.token.value);
const getUserChatId = computed(() => auth.chatId.value);

onMounted(async () => {
  // Initialize webhook
  await useFetch("/api/telegram-webhook", { method: "POST" });

  // Initialize authentication
  await auth.initialize();

  // For debugging purposes
  console.log('TelegramFetch: Auth initialized, token available:', !!auth.token.value);
});
</script>
