import { PrismaClient } from "@prisma/client";

const runtimeConfig = useRuntimeConfig();
const prisma = new PrismaClient();

const endpoint = runtimeConfig.endpoint + "/api/v1/auth/sign-up";
// example: http://127.0.0.1:8000/api/v1/auth/sign-up?name=TestUser&phone=&password=&chatid=1234

interface SignInResponse {
  token: string;
  password: string;
  chatid: string;
  is_phone_verified: number;
  is_email_verified: number;
  is_personal_info: number;
  is_exist_user: number;
  login_type: number;
  email: number;
}

// Endpoit Sign up user
export default defineEventHandler(async (event) => {
  try {
    const { id } = event.context.params as Record<string, string>; // Ensure params are typed

    const checkCustomer = await prisma.customer.findUnique({
      where: {
        chatid: String(id),
      },
    });

    if (checkCustomer) {
      // console.log("User Detail:", checkCustomer);
      console.info("New Customer Found, Creating user....");

      // Sign in user
      try {
        // const uri = `${endpoint}?name=${checkCustomer.username}&phone=${checkCustomer.phone}&password=&chatid=${checkCustomer.chatid}`;
        // const endpoint = "http://127.0.0.1:8000/api/v1/auth/sign-up";
        const params = {
          name: checkCustomer.username,
          phone: checkCustomer.phone,
          // password: "",
          chatid: checkCustomer.chatid,
        };

        try {
          const signInRes = await $fetch<SignInResponse>(`${endpoint}`, {
            headers: {
              moduleId: "1",
              zoneId: "1",
            },
            method: "POST",
            params,
          });
          console.info("User Sign In Success! ID: ", checkCustomer.chatid);
          // console.info(signInRes);
          console.warn("Token: ", await signInRes.token);
          // console.warn("PW: ", await signInRes.password);

          // Update User Token & PW to DB
          if (await signInRes.token) {
            console.warn("Updating User Token & PW to DB....");
            await prisma.customer.update({
              where: {
                chatid: id,
              },
              data: {
                user_token: await signInRes.token,
                hash_pw: await signInRes.password,
              },
            });
            console.warn("User Token & PW Updated to DB");
          }
        } catch (error) {
          console.error("Error During Call API", error);
        }
        return {
          status: 200,
          message: "ok",
          data: checkCustomer.username,
        };
      } catch (error) {
        console.error("Error During Sign In", error);
      }
    } else {
      console.log("User Not Found");
      return {
        status: 404,
        message: "User Not Found",
        data: [],
      };
    }
  } catch (error) {
    console.error(error);
    return {
      status: 500,
      message: "Internal Error" + error,
    };
  }
});
