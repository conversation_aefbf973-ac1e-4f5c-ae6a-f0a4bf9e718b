import { locationManager } from "@telegram-apps/sdk";
export default async function queryLongLat() {
  // MARK: - You have to amount this before request location
  if (locationManager.mount.isAvailable()) {
    await locationManager.mount();
    try {
      if (locationManager.requestLocation.isAvailable()) {
        const location = await locationManager.requestLocation();
        await useFetch("/api/telegram/fetchData", {
          method: "post",
          body: { location },
        });
        return location;
      } else {
        console.error("Request location is not Available");
      }
    } catch {
      console.error("Request location fail");
    }
  }
}
