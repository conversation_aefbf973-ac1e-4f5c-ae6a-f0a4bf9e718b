import { PrismaClient } from "@prisma/client";

// interface CustomerProps {
//   id: string;
//   phone: String;
//   name: string;
//   chatid: string;
//   lang: string;
//   allow_chatbot: boolean;
//   photo_url: string;
//   username: string;
// }
const prisma = new PrismaClient();

export default defineEventHandler(async (event) => {
  // if (event.node.req.method === "GET") {
  //   try {
  //     const users = await prisma.customer.findMany();
  //     const usersWithConvertedChatid = users.map((user) => ({
  //       ...user,
  //       chatid: Number(user.chatid),
  //     }));

  //     if (users.length === 0) {
  //       return {
  //         status: 404,
  //         message: "No User Found",
  //         data: [],
  //       };
  //     }

  //     return {
  //       status: 200,
  //       message: "ok",
  //       data: usersWithConvertedChatid,
  //     };
  //   } catch (error) {
  //     console.error(error);
  //     return {
  //       status: 500,
  //       message: "Internal Error" + error,
  //     };
  //   }
  // }

  // Call for check old Customer
  if (event.node.req.method === "POST") {
    try {
      const body = await readBody(event);
      console.warn(
        "Received Search Customer Body from Telegram:Post--> ",
        body
      );

      // const convertedBody = parseInt(body.id);
      const convertedBody = String(body.id);
      let existUser = false;

      console.warn("Checking User: ", convertedBody);
      const checkCustomer = await prisma.customer.findUnique({
        where: {
          chatid: convertedBody,
        },
      });

      if (checkCustomer) {
        console.warn("Requested User exist");
        existUser = true;

        // Call Login API to get a fresh token
        try {
          const loginRes = await $fetch(`/api/telegram/customer/login`, {
            method: "POST",
            body: {
              id: Number(body.id),
            },
          });

          // After login, verify the token was saved to DB
          const updatedUser = await prisma.customer.findUnique({
            where: {
              chatid: convertedBody,
            },
            select: {
              user_token: true,
            },
          });

          console.warn("Verified token in DB:", updatedUser?.user_token);
          console.warn("Token from login response:", loginRes.data);

          // Ensure the token in the response matches what's in the DB
          return {
            status: 404,
            message: "User Already Exists, Logged in",
            chatid: body.id,
            loginRes,
            data: loginRes.data // Include the token directly in the data field for consistency
          };
        } catch (error) {
          console.error("Error After API Called.", error);
        }
      }

      if (existUser !== true) {
        console.warn("This user is new: ", existUser);
        return {
          status: 200,
          message: "This user does not exist.",
          exist: false,
        };
      }

      // console.log("Check Customer Status: ", checkCustomer);
      return {
        status: 404,
        message: "User Already Exist",
        exist: true,
      };
    } catch (error) {
      console.error(error);
      return {
        status: 500,
        message: "Internal Error" + error,
      };
    }
  }
});
