<template>
  <div class="container mx-auto px-4 py-8">
    <!-- Debug token display (can be removed in production) -->
    <!-- <div v-if="getUserToken" class="text-xs text-gray-500 mb-2">
      User Token: {{ getUserToken }}
    </div> -->

    <!-- Header -->
    <div class="text-center mb-8">
      <h1 class="text-3xl font-bold mb-2">Your Shopping Cart</h1>
      <p class="font-bold" v-if="cart.length > 0">
        Total Items: ({{ cart.length }})
      </p>
    </div>

    <!-- Empty State -->
    <div v-if="cart.length === 0" class="text-center py-12">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24"
        stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
          d="M3 3h2l.4 2M7 13h10l4-8H5.4M7 13L5.4 5M7 13l-2.293 2.293c-.63.63-.184 1.707.707 1.707H17m0 0a2 2 0 100 4 2 2 0 000-4zm-8 2a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
      <h3 class="mt-4 text-lg font-medium">Your cart is empty</h3>
      <p class="mt-1 opacity-70">Start adding some amazing products!</p>
      <NuxtLink to="/">
        <button class="mt-6 px-6 py-3 btn btn-primary transition-all duration-300 ease-in-out">
          Continue Shopping
        </button>
      </NuxtLink>
    </div>

    <!-- Cart Items -->
    <div v-else class="space-y-6">
      <!-- Cart Item -->
      <div v-for="(product, index) in cart" :key="index"
        class="flex flex-col sm:flex-row gap-4 p-4 border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
        <!-- Product Image -->
        <div class="relative flex-shrink-0 w-24 h-24 sm:w-30 sm:h-32">
          <img :src="product.item.image_full_url" :alt="product.item.name"
            class="absolute inset-0 w-full h-full object-contain transition-all duration-300 hover:scale-105"
            loading="lazy" @error="handleImageError" />
          <div
            class="absolute inset-0 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-300 bg-black/10 rounded-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-white/80" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>

        <!-- Product Details -->
        <div class="flex-grow">
          <div class="flex justify-between">
            <div>
              <div class="flex flex-col items-center text-center">
                <h3 class="text-sm font-semibold text-gray-900">
                  {{ product.item.name }}
                </h3>
                <!-- If there is a discount, show original and discounted prices -->
                <div v-if="product.item.discount > 0">
                  <!-- Original Price (strikethrough) -->
                  <span class="text-gray-400 line-through mr-2">
                    ${{ (product.price * product.quantity).toFixed(2) }}
                  </span>

                  <!-- Discounted Price -->
                  <span class="text-red-600 font-semibold">
                    ${{
                      (product.item.discount_type === "percent"
                        ? product.price * product.quantity -
                        (product.price *
                          product.quantity *
                          product.item.discount) /
                        100
                        : product.price * product.quantity -
                        product.item.discount * product.quantity
                      ).toFixed(2)
                    }}
                  </span>
                </div>

                <!-- No discount: show only regular price -->
                <div v-else>
                  <span class="text-gray-600 font-medium">
                    ${{ (product.price * product.quantity).toFixed(2) }}
                  </span>
                </div>
              </div>

              <p v-if="product.variation && product.variation[0]?.type"
                class="text-sm text-gray-600 mt-1 flex items-center">
                <span class="mr-1.5 text-gray-500">Variant:</span>
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-50 text-orange-700 border border-orange-200">
                  {{ product.variation[0].type }}
                </span>
              </p>
            </div>
            <button @click="removeProduct(product.id)"
              class="p-2 rounded-2xl border border-red-500 text-red-500 hover:bg-red-100 transition-colors duration-200"
              aria-label="Remove item">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            </button>
          </div>

          <!-- Price & Quantity -->
          <div class="mt-4 flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <div class="flex items-center">
              <!-- <span class="text-gray-900 font-medium"
                >${{ product.price.toFixed(2) }}</span
              > -->
              <span class="mx-2 text-gray-400"></span>
              <div class="flex items-center rounded-md -ml-4">
                <div class="flex items-center border border-gray-300 rounded-md overflow-hidden">
                  <!-- Decrease Button -->
                  <button class="px-3 py-1 text-lg text-gray-600 hover:bg-gray-100"
                    @click="updateQuantity(product.id, product.quantity - 1)" :disabled="product.quantity <= 1">
                    &minus;
                  </button>

                  <!-- Quantity -->
                  <span class="join-item px-4 py-1 flex items-center justify-center border border-base-300">
                    {{ product.quantity }}
                  </span>

                  <!-- Increase Button -->
                  <button class="px-3 py-1 text-lg text-gray-600 hover:bg-gray-100"
                    @click="updateQuantity(product.id, product.quantity + 1)">
                    &#43;
                  </button>
                </div>
              </div>
            </div>
            <div class="flex items-center">
              <!-- other elements here -->

              <div class="ml-auto text-lg font-semibold text-gray-700">
                ${{
                  (product.item.discount_type === "percent"
                    ? product.price * product.quantity -
                    (product.price * product.quantity * product.item.discount) /
                    100
                    : product.price * product.quantity -
                    product.item.discount * product.quantity
                  ).toFixed(2)
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Order Summary -->
    <div v-show="cart.length > 0">
      <div class="bg-gray-50 p-6 rounded-lg">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Order Summary</h3>
        <div class="space-y-3">
          <div class="flex justify-between">
            <span class="text-gray-600">Subtotal</span>
            <span class="font-medium">${{ grandTotal.toFixed(2) }}</span>
          </div>
          <div class="flex justify-between">
            <span class="text-gray-600">Shipping</span>
            <span class="font-medium">0.00 $</span>
          </div>
          <div class="pt-3 border-t border-gray-200 flex justify-between">
            <span class="text-md font-medium">Total Discount</span>
            <span class="text-md font-bold">-${{ totalDiscount.toFixed(2) }}</span>
          </div>
          <div class="pt-3 border-t border-gray-200 flex justify-between">
            <span class="text-lg font-medium">Total</span>
            <span class="text-lg font-bold">${{ (grandTotal - totalDiscount).toFixed(2) }}</span>
          </div>
        </div>

        <!-- Checkout Buttons -->
        <div class="mt-6 space-y-3">
          <NuxtLink to="checkout">
            <button
              class="w-full py-3 px-6 rounded-xl btn btn-primary hover:shadow-lg transition-all duration-300 ease-in-out">
              Proceed to Checkout
            </button>
          </NuxtLink>
          <NuxtLink to="/">
            <button
              class="w-full flex items-center justify-center mt-3 gap-2 btn btn-outline btn-neutral transition-all duration-300 ease-in-out group">
              <svg xmlns="http://www.w3.org/2000/svg"
                class="h-5 w-5 transition-transform duration-300 group-hover:-translate-x-1" fill="none"
                viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18" />
              </svg>
              <span>Continue Shopping</span>
            </button>
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, computed } from "vue";
import axios from "axios";
import { useAuth } from "~/composable/useAuth";

definePageMeta({
  layout: "custom",
});

export default {
  setup() {
    const cart = ref([]);
    const isLoading = ref(false);
    const phone = ref("");
    const name = ref("");
    const { apiUrl, moduleId, storeId, zoneId, apiPrefix } =
      useRuntimeConfig().public;

    // Use the auth composable
    const auth = useAuth();

    // For backward compatibility and template binding
    const getUserToken = computed(() => auth.token.value);
    const getUserChatId = computed(() => auth.chatId.value);

    // Computed property for grand total
    const grandTotal = computed(() => {
      return cart.value.reduce((total, product) => {
        return total + product.price * product.quantity;
      }, 0);
    });

    // Fetch cart data from API
    const getCartList = async () => {
      try {
        isLoading.value = true;
        const URL = `${apiUrl}${apiPrefix}/customer/cart/list`;

        // Use the user token if available
        const headers = {
          moduleId: `${moduleId}`,
        };

        if (auth.token.value) {
          headers.Authorization = `Bearer ${auth.token.value}`;
        }

        const response = await axios.get(URL, { headers });
        cart.value = response.data;
      } catch (error) {
        console.error("Error fetching cart data:", error);
      } finally {
        isLoading.value = false;
      }
    };
    // total discount
    const totalDiscount = computed(() => {
      return cart.value.reduce((total, product) => {
        const discount = product.item.discount || 0;
        const quantity = product.quantity || 1;
        const price = product.price || 0;
        const type = product.item.discount_type;

        if (type === "percent") {
          return total + (price * quantity * discount) / 100;
        } else {
          return total + discount * quantity;
        }
      }, 0);
    });

    // Remove product from cart
    const removeProduct = async (productId) => {
      try {
        const baseURL = `${apiUrl}${apiPrefix}/customer/cart/remove-item?cart_id=${productId}`;

        const headers = {
          moduleId: moduleId,
        };

        if (auth.token.value) {
          headers.Authorization = `Bearer ${auth.token.value}`;
        }

        await axios.delete(baseURL, { headers });
        await getCartList();
      } catch (error) {
        console.error("Error removing product:", error);
      }
    };

    // Update product quantity
    const updateQuantity = async (productId, newQuantity) => {
      if (newQuantity < 1) return;

      try {
        const headers = {
          moduleId: moduleId,
          Authorization: `Bearer ${getUserToken.value}`,
        };

        // Find the product in the cart
        const productIndex = cart.value.findIndex(
          (item) => item.id === productId
        );
        if (productIndex === -1) {
          console.error("Product not found in cart");
          return;
        }

        // Save original quantity for potential rollback
        const originalQuantity = cart.value[productIndex].quantity;

        // Optimistically update the UI immediately
        cart.value[productIndex].quantity = newQuantity;
        console.log("newQuantity", newQuantity);

        // Make the API call
        const response = await axios.post(
          `${apiUrl}${apiPrefix}/customer/cart/update`,
          {
            cart_id: productId,
            quantity: newQuantity,
            price: cart.value[productIndex].price,
          },
          { headers }
        );

        if (response.data.success) {
          // Revert if API call fails
          cart.value[productIndex].quantity = originalQuantity;
          console.error("Failed to update quantity on server");
        }
      } catch (error) {
        console.error("Error updating quantity:", error);
        // In case of error, refresh the cart to get the correct state from server
        await getCartList();
      }
    };

    onMounted(async () => {
      // Initialize auth if not already initialized
      if (!auth.isAuthenticated.value) {
        await auth.initialize();
      }

      // Update profile data if available
      if (auth.userProfile.value) {
        phone.value = auth.userProfile.value.phone || "";
        name.value = auth.userProfile.value.name || "";
      }

      // Fetch cart data
      await getCartList();

      console.log(
        "Cart: Auth initialized, token available:",
        !!auth.token.value
      );
    });

    return {
      cart,
      isLoading,
      grandTotal,
      totalDiscount, // ✅ Don't forget this
      removeProduct,
      updateQuantity,
      moduleId,
      getUserToken,
      getUserChatId,
      phone,
      name,
    };
  },
};
</script>

<style>
/* Custom styles can be added here */
</style>
