import { PrismaClient } from "@prisma/client";
import { Telegraf, Telegram } from "telegraf";

const runtimeConfig = useRuntimeConfig();
const prisma = new PrismaClient();
const bot = new Telegraf(runtimeConfig.telegramToken);

export async function botRegister(
  bot_name: string,
  bot_notify_to_id: number,
  bot_notify_to_group_name: string,
  bot_notify_group_privilige: string
) {
  console.warn(`Recieved Request to Create Bot Record:`);
  const checkgroupID = await prisma.botInfo.findFirst({
    where: {
      bot_notify_to_id: bot_notify_to_id,
    },
  });

  if (!checkgroupID?.bot_notify_to_id) {
    console.warn(`Creating Bot Record....`);

    await prisma.botInfo.create({
      data: {
        bot_name: bot_name,
        bot_notify_to_id: bot_notify_to_id,
        bot_notify_to_group_name: bot_notify_to_group_name,
        bot_notify_group_privilige: bot_notify_group_privilige,
      },
    });

    console.warn(`Bot Record Created Successfully!`, bot_notify_to_group_name);
    return `${bot_notify_to_id} successfully inserted`;
  }
  return `${bot_notify_to_id} already exist`;
}
