<template>
<div>
   <div 
  v-if="products.length === 0"
  class="flex flex-col items-center justify-center py-12 px-4 bg-gray-50 rounded-lg shadow-sm"
>
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    class="h-16 w-16 text-gray-400 mb-4" 
    fill="none" 
    viewBox="0 0 24 24" 
    stroke="currentColor"
    aria-hidden="true"
  >
    <path 
      stroke-linecap="round" 
      stroke-linejoin="round" 
      stroke-width="2" 
      d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
    />
  </svg>
  <h3 class="text-2xl font-semibold text-gray-800 mb-2">
    No Products Found
  </h3>
  <p class="text-gray-600 text-center max-w-md">
    We couldn't find any products matching your criteria.
  </p>

</div>
   <!-- <button class="btn btn-primary" v-on:click="$router.push('/products')"> Click me to product</button> -->
<div
    class="container mx-auto grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2"
  >
   <div v-if="products.length === 0" style="font-weight:bold; text-align:center; font-size:20px;" class="text-center text-gray-900  py-8">
    </div>
   
    <div
      v-for="product in products" v-else
      :key="product.id"
      class="relative mt-4 w-full overflow-hidden rounded-lg bg-white shadow-md"
    >
      <NuxtLink :to="'/products/' + product.id">
        <figure class="figure justify-center">
          <img
            class="h-32 w-32 rounded-t-lg object-contain mx-auto"
            :src="product.image_full_url"
            alt="product image"
          />
        </figure>
      </NuxtLink>
     <!-- <span
        class="absolute top-0 left-0 w-28 translate-y-4 -translate-x-6 -rotate-45 bg-blue-600 text-center text-sm text-white"
        >-{{product.discount}}% OFF</span> -->
        <span class="absolute top-2 left-2 bg-gradient-to-br from-red-500 to-rose-600 text-white text-xs font-extrabold uppercase tracking-wider px-3 py-1 rounded-full shadow-lg flex items-center justify-center z-10 transform rotate-0 hover:rotate-[-4deg] transition-transform" v-if="product.discount !== 0">-{{ product.discount }}% OFF</span>
   <span class="absolute top-2 left-2 bg-gradient-to-br from-red-500 to-rose-600 text-white text-xs font-extrabold uppercase tracking-wider px-3 py-1 rounded-full shadow-lg flex items-center justify-center z-10 transform rotate-0 hover:rotate-[-4deg] transition-transform" v-else>
     0% OFF
    </span> 
      <div class="card-body mt-4 px-5 pb-5">
        <a href="#">
          <h5
            class="text-md tracking-tight text-slate-700 truncate font-bold whitespace-nowrap"
          >
            {{ product.name }}
          </h5>
        </a>
        <div class="flex items-center justify-between">
          <p>
            <span class="text-xl font-semibold">
              <span
      v-if="product.discount !== 0"
      class="text-xl text-center font-semibold"
    >
          <span
      class="text-xl text-center  font-semibold"
      :class="{ 'line-through text-gray-500': product.discount !== 0 }"
    >
      ${{ product.price.toFixed(2) }}
    </span>
    <span class="text-green-500">
      ${{
        (product.price - (product.price * product.discount) / 100).toFixed(2)
      }}
  </span>
    </span>

    <!-- Show Original Price if No Discount Exists -->
    <span
      v-else
      class="text-xl text-green-500 text-center font-semibold"
    >
      ${{ product.price.toFixed(2) }}
    </span>
            </span>
          </p>
        </div>
      </div>
    </div>
  </div>
    

  </div>

</template>

<script setup>
import { ref, onMounted } from "vue";
import axios from "axios";
import { useRoute } from 'vue-router';
const route = useRoute();
const products = ref([]);
const moduleId = "1";
const categoryId = route.params.id;
console.log("categories",categoryId);
//const AuthToken = "********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************";
//console.log("Token",AuthToken);
const { apiUrl, apiPrefix } = useRuntimeConfig().public;
const addToCart = async (itemId, price) => {
  const quantity = 1; // Set the quantity as needed
  const uri = `${apiUrl}{apiPrefix}/customer/cart/add?item_id=${itemId}&model=Item&price=${price}&quantity=${quantity}`;
  console.log("Add to cart", uri);
  try {
    const response = await axios.post(uri, null, {
      headers: {
        moduleId: "1",
        Authorization:
          "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
      },
    });
    console.log("Item added to cart:", response);
  } catch (error) {
    console.error("Error adding item to cart:", error);
  }
};
const fetchProducts = async () => {
  try {
    const { apiUrl, apiPrefix } = useRuntimeConfig().public;
    const response = await axios.get(`${apiUrl}${apiPrefix}/items/latest`, {
      params: {
        store_id: 2,
        category_id: `${categoryId}`,
        limit: 1000,
        offset: 1,
      },
      headers: {
        moduleId: "1", // Replace with your actual moduleId
        zoneId: "[1]", // Replace with your actual zoneId
      },
    });
    console.log("Products", response.data.products);
    products.value = response.data.products; // Assuming the response data is the product list
  } catch (error) {
    console.error("Error fetching products:", error);
  }
};

onMounted(() => {
  fetchProducts();
});
</script>

<style scoped>
/* Add any additional styles here */
</style>
