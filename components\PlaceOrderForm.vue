<template>
 <form @submit.prevent="submitForm">
   <div class="rounded-lg w-full lg:mt-0">
     <!-- <p class="text-xl font-medium">Order Details</p>
      <p class="text-gray-400">
        Complete your order by providing your basic information.
      </p> -->
    <div>
       <!-- <label for="name" class="mt-4 mb-2 block text-sm font-medium">Name</label>
        <input type="text" placeholder="John Doe" class="input input-primary w-full" v-model="name" />

        <label for="phone" class="mt-4 mb-2 block text-sm font-medium">Phone Number</label>
        <input type="text" placeholder="012 555 888" class="input input-primary w-full" required v-model="phone" />

        <label for="address" class="mt-4 mb-2 block text-sm font-medium">Address</label>
        <div class="join w-full">
          <input type="text" placeholder="Toulouk, Dakar" class="input input-primary w-full join-item"
            v-model="address" />
          <button @click.prevent="getLocation" class="btn btn-primary join-item">
            <Icon style="color: whitesmoke;" icon="map:postal-code" />
          </button>
        </div> -->


      <!--  <label for="payment-method" class="mt-4 mb-2 block text-sm font-medium">Payment Method</label>
        <div class="form-control">
          <label class="label cursor-pointer">
            <span class="label-text">KHQR Payment</span>
            <input type="radio" name="payment-method" v-model="paymethod" value="KHQR Payment"
              class="radio checked:radio-primary" />
          </label>
          <label class="label cursor-pointer">
            <span class="label-text">Cash on Delivery</span>
            <input type="radio" name="payment-method" v-model="paymethod" value="cash_on_delivery"
              class="radio checked:radio-primary" checked />
          </label>
        </div> -->

        <!-- Total -->

        <!--  <div class="flex items-center justify-between">
            <p class="text-sm font-medium text-gray-900">Subtotal</p>
            <p class="font-semibold text-gray-900"> ${{
        (price - (price * discount) / 100).toFixed(2)
      }}</p>
          </div> -->
           <!-- <div class="flex items-center justify-between">
            <p class="text-sm font-medium text-gray-900">Discount</p>
            <p class="font-semibold text-gray-900">-${{
        ((price * discount) / 100).toFixed(2)
      }}</p>
          </div> -->
         <!-- <div class="flex items-center justify-between">
            <p class="text-sm font-medium text-gray-900">Shipping</p>
            <p class="font-semibold text-gray-900">${{ shipping.toFixed(2) }}</p>
          </div> -->
        </div>
       <!-- <div class="mt-6 flex items-center justify-between">
          <p class="text-sm font-medium text-gray-900">Total</p>
          <p class="text-2xl font-semibold text-gray-900">{{ formatNumber }}</p>
        </div> -->

     <!-- <button class="mt-4 w-full btn btn-primary" type="submit">
        Place Order
      </button>  -->
    </div>
  </form>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import axios from "axios";
import { useAuth } from '~/composable/useAuth';

const name = ref("");
const phone = ref("");
const address = ref("");

// Use the auth composable
const auth = useAuth();

// For backward compatibility and template binding
const getUserToken = computed(() => auth.token.value);
const getUserChatId = computed(() => auth.chatId.value);

const userstaus = ref();
const paymethod = ref("cash_on_delivery");

const props = defineProps({
  total: {
    type: Number,
    required: true,
  },
  shipping: {
    type: Number,
    required: true,
  },
  price: {
    type: Number,
    required: true,
  },
  product: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  phone: {
    type: String,
    required: true,
  },
  image: {
    type: String,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  variation: {
    type: String,
    required: true,
  },

  quantity: {
    type: Number,
    required: true,
  },
   discount: {
    type: String,
    required: true,
  },
});

const formatNumber = computed(() => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(props.total);
});

// Fetch user info and populate fields
onMounted(async () => {
  // Initialize auth if not already initialized
  if (!auth.isAuthenticated.value) {
    await auth.initialize();
  }

  // Update profile data if available
  if (auth.userProfile.value) {
    phone.value = auth.userProfile.value.phone || "None was taken";
    name.value = auth.userProfile.value.name || "";
  }
});

const long = ref(0);
const lat = ref(0);
const apiKey = 'AIzaSyCmIZXqX8-6P9kC9f6XTUB6cvEACU51DCU'; // Replace with your actual Google Maps API key

async function getLocation() {
  try {
    // Step 1: Get latitude and longitude from getLocationTelegram()
    const { longitude, latitude } = await getLocationTelegram();

    // Step 2: Update the longitude and latitude values
    long.value = longitude;
    lat.value = latitude;

    // Step 3: Fetch the address using the Google Maps Geocoding API
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`;
    const response = await axios.get(url);

    // Step 4: Check if the response contains valid address data
    if (response.data.results.length > 0) {
      address.value = response.data.results[0].formatted_address;
      console.log("Address:", address.value);
    } else {
      address.value = 'Address not found';
      console.log("Address not found");
    }
  } catch (error) {
    // Handle errors
    console.error('Error fetching location or address:', error);
    address.value = 'Error fetching address';
  }
}

function submitForm() {
  if (!name.value) name.value = "Unknown";
  if (!address.value) address.value = "Contact for address";

  const { apiUrl, apiPrefix } = useRuntimeConfig().public;
  addItemToCart();
  placeOrder();

  // Step 1: Check if the phone number exists
}

async function addItemToCart() {
  const { apiUrl, apiPrefix } = useRuntimeConfig().public;
  const cartData = {
    item_id: props.id,
    model: "Item",
    price: props.price,
    quantity: props.quantity,
    testing: props.variation,
    variation: JSON.stringify(props.variation && props.variation !== "N/A" ? [{ type: props.variation.replace(/\s+/g, '') }] : [])
  };
  console.log("cartData", cartData);
  const cartUri = `${apiUrl}${apiPrefix}/customer/cart/add`;


  try {
    await axios.post(cartUri, null, {
      params: cartData,
      headers: {
        moduleId: "1",
        Authorization: auth.token.value ? `Bearer ${auth.token.value}` : '',
      },
    });
    console.log("Item added to cart successfully.");
    await placeOrder(); // Proceed to place the order after adding item to cart
  } catch (error) {
    console.error("Error adding item to cart:", error.message);
  }
}

// debug_token.value = '***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************'
// debug_id.value = 400807183

async function placeOrder() {
  const orderData = {
    order_amount: props.total,
    price: props.price,
    product: props.product,
    shipping: props.shipping,
    image: props.image,
    quantity: props.quantity,
    discount: props.discount,

    payment_method: paymethod.value,
    order_type: "delivery",
    store_id: 2,
    distance: 2,
    address: address.value,
    name: name.value,
    phone: phone.value,
    token: auth.token.value || '',
    chatid: auth.chatId.value || '',
    longitude: long.value,
    latitude: lat.value,
  };

  navigateTo({
    name: "confirm-id",
    params: { id: props.id },
    query: orderData,
  });
}


</script>

<style>
/* Add your custom styles here */
</style>
