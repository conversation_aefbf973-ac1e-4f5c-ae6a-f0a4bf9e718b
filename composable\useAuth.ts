import { ref, readonly } from "vue";
import { init, retrieveLaunchParams, requestContact } from "@telegram-apps/sdk";
import { isTMA } from "@telegram-apps/sdk";

// Create a state with reactive references
const token = ref<string | null>(null);
const chatId = ref<string | null>(null);
const isAuthenticated = ref(false);
const userProfile = ref<any>(null);
const isLoading = ref(false);

// Create a composable to manage authentication
export function useAuth() {
  // Initialize auth state from localStorage on client side
  const initializeFromStorage = () => {
    if (import.meta.client) {
      const storedToken = localStorage.getItem("userToken");
      const storedChatId = localStorage.getItem("userChatId");
      const storedUserProfile = localStorage.getItem("userProfile");

      if (storedToken) {
        token.value = storedToken;
        isAuthenticated.value = true;
        console.log("Auth: Token retrieved from localStorage");
      }

      if (storedChatId) {
        chatId.value = storedChatId;
      }

      if (storedUserProfile) {
        try {
          userProfile.value = JSON.parse(storedUserProfile);
          console.log("Auth: User profile retrieved from localStorage");
        } catch (error) {
          console.error(
            "Auth: Error parsing user profile from localStorage:",
            error
          );
        }
      }
    }
  };

  // Save token to localStorage
  const saveTokenToStorage = (newToken: string, newChatId: string | null) => {
    if (import.meta.client) {
      localStorage.setItem("userToken", newToken);
      if (newChatId) {
        localStorage.setItem("userChatId", newChatId);
      }
      if (userProfile.value) {
        try {
          localStorage.setItem(
            "userProfile",
            JSON.stringify(userProfile.value)
          );
          console.log("Auth: User profile saved to localStorage");
        } catch (error) {
          console.error(
            "Auth: Error saving user profile to localStorage:",
            error
          );
        }
      }
      console.log("Auth: Token saved to localStorage");
    }
  };

  // Set token and update state
  const setToken = (newToken: string, newChatId: string | null = null) => {
    token.value = newToken;
    if (newChatId) {
      chatId.value = newChatId;
    }
    isAuthenticated.value = !!newToken;
    saveTokenToStorage(newToken, newChatId);
  };

  // Clear token and update state
  const clearToken = () => {
    token.value = null;
    chatId.value = null;
    userProfile.value = null;
    isAuthenticated.value = false;
    if (import.meta.client) {
      localStorage.removeItem("userToken");
      localStorage.removeItem("userChatId");
      localStorage.removeItem("userProfile");
      console.log(
        "Auth: Token, chatId, and userProfile cleared from localStorage"
      );
    }
  };

  // Fetch user token from API
  const fetchUserToken = async (tgWebAppData: any) => {
    try {
      isLoading.value = true;
      console.log("Auth: Fetching user token for ID:", tgWebAppData.user?.id);

      const { data } = await useFetch("/api/telegram/customer/getToken", {
        method: "POST",
        body: {
          id: tgWebAppData.user?.id,
          auth_date: tgWebAppData.auth_date,
          hash: tgWebAppData.hash,
        },
      });

      if (data.value?.data) {
        const newToken = data.value.data;
        console.log("Auth: Received token from getToken API");

        // Update user profile if available
        if (data.value?.profile) {
          userProfile.value = data.value.profile;
        }

        // Set token and update state
        setToken(newToken, tgWebAppData.user?.id);
        return newToken;
      } else {
        console.warn("Auth: No token received from getToken API");
        return null;
      }
    } catch (error) {
      console.error("Auth: Error fetching user token:", error);
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  // Handle user login
  const handleLogin = async (tgWebAppData: any) => {
    try {
      isLoading.value = true;

      // Initialize Telegram SDK
      await init();
      console.log("Auth: Telegram SDK initialized");

      // Get user ID from Telegram data
      const chatId = tgWebAppData?.user?.id;

      if (!chatId) {
        console.error("Auth: No Telegram user ID available");
        return false;
      }

      console.log("Auth: Found Telegram user ID:", chatId);

      // Check if user exists in our system
      const { data: customerData } = await useFetch(
        "/api/telegram/getTelegramCustomer",
        {
          method: "POST",
          body: { id: chatId },
        }
      );

      // If user is new (status 200 means user doesn't exist yet)
      if (customerData.value?.status === 200) {
        // Request contact information if available
        if (requestContact.isAvailable()) {
          let phoneNumber = "";

          try {
            const contact = await requestContact();
            phoneNumber = contact?.contact?.phone_number ?? "";
            console.log("Auth: Contact information:", contact);
          } catch (error) {
            console.log(
              "Auth: User declined to share contact or contact not available",
              error
            );
          }

          // Create new user data
          const userData = {
            id: chatId,
            phone: phoneNumber,
            name: tgWebAppData?.user?.first_name,
            lang: tgWebAppData?.user?.language_code,
            allow_write_to_pm: tgWebAppData?.user?.allows_write_to_pm,
            photo_url: tgWebAppData?.user?.photo_url,
            username: tgWebAppData?.user?.username,
          };

          // Set user profile from the data we have
          userProfile.value = {
            phone: phoneNumber,
            name: tgWebAppData?.user?.first_name,
            // Add other fields as needed
          };
          console.log("Auth: User profile set from registration data");

          // Register the new user
          const { data: registrationResult } = await useFetch(
            "/api/telegram/postTelegramCustomer",
            {
              method: "POST",
              body: userData,
            }
          );

          console.log(
            "Auth: User registration result:",
            registrationResult.value
          );

          // After registration, get the token
          await fetchUserToken(tgWebAppData);
        }
      } else if (customerData.value?.loginRes?.data) {
        // User exists and we have a token from the login response
        const newToken = customerData.value.loginRes.data;

        // Update user profile if available
        if (customerData.value.profile) {
          userProfile.value = customerData.value.profile;
          console.log("Auth: User profile updated from login response");
        } else if (tgWebAppData?.user) {
          // If no profile in response, fetch user data from postTelegramCustomer endpoint
          const { data: userData } = await useFetch(
            "/api/telegram/postTelegramCustomer",
            {
              method: "POST",
              body: {
                id: chatId,
                name: tgWebAppData.user.first_name,
                username: tgWebAppData.user.username,
                photo_url: tgWebAppData.user.photo_url,
                lang: tgWebAppData.user.language_code,
                allow_write_to_psm: tgWebAppData.user.allows_write_to_pm,
              },
            }
          );

          console.log(
            "Auth: User data from postTelegramCustomer:",
            userData.value
          );

          if (userData.value?.data && userData.value.data.phone) {
            // User exists and has a phone number
            userProfile.value = {
              name: userData.value.data.name || tgWebAppData.user.first_name,
              phone: userData.value.data.phone,
              username:
                userData.value.data.username || tgWebAppData.user.username,
            };
            console.log(
              "Auth: User profile updated with phone from postTelegramCustomer"
            );
          } else {
            // User exists but doesn't have a phone number, try to get it
            let phoneNumber = "";

            if (requestContact.isAvailable()) {
              try {
                const contact = await requestContact();
                phoneNumber = contact?.contact?.phone_number ?? "";
                console.log("Auth: Contact information:", contact);

                // If we got a phone number, update the user profile
                if (phoneNumber) {
                  // Update user data with phone number
                  await useFetch("/api/telegram/postTelegramCustomer", {
                    method: "POST",
                    body: {
                      id: chatId,
                      phone: phoneNumber,
                      name: tgWebAppData.user.first_name,
                      username: tgWebAppData.user.username,
                      photo_url: tgWebAppData.user.photo_url,
                      lang: tgWebAppData.user.language_code,
                      allow_write_to_psm: tgWebAppData.user.allows_write_to_pm,
                    },
                  });
                }
              } catch (error) {
                console.log(
                  "Auth: User declined to share contact or contact not available",
                  error
                );
              }
            }

            // Set user profile with whatever data we have
            userProfile.value = {
              name: tgWebAppData.user.first_name,
              phone: phoneNumber,
              username: tgWebAppData.user.username,
            };
            console.log("Auth: User profile created with available data");
          }
        }

        // Set token and update state
        setToken(newToken, chatId);
      } else {
        // User exists but no token in response, fetch token explicitly
        await fetchUserToken(tgWebAppData);
      }

      return true;
    } catch (error) {
      console.error("Auth: Error in handleLogin:", error);
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  // Initialize authentication
  const initialize = async () => {
    // First check localStorage for existing token
    initializeFromStorage();

    // If we're in Telegram Mini App environment, try to authenticate
    if (isTMA()) {
      console.log("Auth: Detected Telegram Mini App environment");

      try {
        // Initialize Telegram SDK
        await init();
        console.log("Auth: Telegram SDK initialized");

        // Get Telegram Web App data
        const { tgWebAppData } = retrieveLaunchParams();
        console.log("Auth: Telegram Web App data retrieved");

        // If we don't have a token yet, try to get one
        if (!token.value) {
          // First try to get token from the getToken endpoint
          const newToken = await fetchUserToken(tgWebAppData);
          console.log("Auth: Token from fetchUserToken:", newToken);

          // If no token was found, try login
          if (!newToken) {
            console.log(
              "Auth: No token from fetchUserToken, trying handleLogin"
            );
            await handleLogin(tgWebAppData);
          }
        }

        // If we have a token but no user profile or no phone number, try to get it
        if (
          token.value &&
          (!userProfile.value || !userProfile.value.phone) &&
          tgWebAppData?.user
        ) {
          // First try to fetch user data from postTelegramCustomer endpoint
          const { data: userData } = await useFetch(
            "/api/telegram/postTelegramCustomer",
            {
              method: "POST",
              body: {
                id: tgWebAppData.user.id,
                name: tgWebAppData.user.first_name,
                username: tgWebAppData.user.username,
                photo_url: tgWebAppData.user.photo_url,
                lang: tgWebAppData.user.language_code,
                allow_write_to_psm: tgWebAppData.user.allows_write_to_pm,
              },
            }
          );

          console.log(
            "Auth: User data from postTelegramCustomer during initialization:",
            userData.value
          );

          if (userData.value?.data && userData.value.data.phone) {
            // User exists and has a phone number
            userProfile.value = {
              name: userData.value.data.name || tgWebAppData.user.first_name,
              phone: userData.value.data.phone,
              username:
                userData.value.data.username || tgWebAppData.user.username,
            };
            console.log(
              "Auth: User profile updated with phone from postTelegramCustomer during initialization"
            );
          } else {
            // User exists but doesn't have a phone number, try to get it
            let phoneNumber = "";

            if (requestContact.isAvailable()) {
              try {
                const contact = await requestContact();
                phoneNumber = contact?.contact?.phone_number ?? "";
                console.log(
                  "Auth: Contact information during initialization:",
                  contact
                );

                // If we got a phone number, update the user data
                if (phoneNumber) {
                  // Update user data with phone number
                  await useFetch("/api/telegram/postTelegramCustomer", {
                    method: "POST",
                    body: {
                      id: tgWebAppData.user.id,
                      phone: phoneNumber,
                      name: tgWebAppData.user.first_name,
                      username: tgWebAppData.user.username,
                      photo_url: tgWebAppData.user.photo_url,
                      lang: tgWebAppData.user.language_code,
                      allow_write_to_psm: tgWebAppData.user.allows_write_to_pm,
                    },
                  });
                }
              } catch (error) {
                console.log(
                  "Auth: User declined to share contact or contact not available during initialization",
                  error
                );
              }
            }

            // Set user profile with whatever data we have
            userProfile.value = {
              name: tgWebAppData.user.first_name,
              phone: phoneNumber,
              username: tgWebAppData.user.username,
            };
            console.log(
              "Auth: User profile created with available data during initialization"
            );
          }

          // Save the updated state to localStorage
          saveTokenToStorage(token.value, chatId.value);
        }

        // Verify we have a token now
        if (token.value) {
          console.log("Auth: Successfully obtained token");
        } else {
          console.warn("Auth: Failed to obtain token after multiple attempts");
        }
      } catch (error) {
        console.error("Auth: Error in initialize:", error);
      }
    } else {
      console.log("Auth: Not in Telegram Mini App environment");
    }
  };

  // Return public interface
  return {
    // State (readonly to prevent direct modification)
    token: readonly(token),
    chatId: readonly(chatId),
    isAuthenticated: readonly(isAuthenticated),
    userProfile: readonly(userProfile),
    isLoading: readonly(isLoading),

    // Methods
    initialize,
    fetchUserToken,
    handleLogin,
    setToken,
    clearToken,
  };
}
