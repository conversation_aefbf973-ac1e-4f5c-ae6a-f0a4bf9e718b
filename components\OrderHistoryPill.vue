<template>
  <div>
    <ul class="space-y-4">
      <li v-for="(order, index) in orderData" :key="index" @click="$router.push(`/history/${order.id}`)"
        class="border p-4 rounded-lg hover:cursor-pointer">
        <div class="flex justify-between">
          <div>
            <h2 class="font-semibold">Order #{{ order.id }}</h2>
            <p class="text-gray-400">Date: {{ order.date }}</p>
          </div>
          <div class="text-right">
            <p class="text-lg font-bold">{{ order.total }}</p>
            <p :class="statusClass(order.status)">Status: {{ order.status }}</p>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import type { PropType } from 'vue';

const OrderProps = defineProps({
  orderData: {
    type: Array as PropType<{ id: number; date: string; total: string; status: string }[]>,
    required: true
  }
})

const statusClass = (status: string) => {
  switch (status) {
    case 'Delivered':
      return 'text-green-500';
    case 'Processing':
      return 'text-blue-500';
    case 'Cancelled':
      return 'text-red-500';
    case 'On Deilvery':
      return 'text-yellow-500';

    default:
      return 'text-gray-500';
  }
}

</script>

<style></style>