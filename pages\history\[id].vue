<template>
  <div class="container mx-auto max-h-screen">
    <div
      class="relative my-5 flex h-full flex-col overflow-hidden rounded-2xl bg-white text-gray-600 shadow-lg ring-1 ring-gray-200">
      <div class="border-b p-6">
        <h6 class="mb-2 text-xl font-semibold">Orders overview</h6>
        <p class="mb-2 text-sm font-normal">
          <i class="inline-block font-black not-italic text-green-600" aria-hidden="true"></i>
          <span class="font-semibold"># {{ id }}</span>
        </p>
      </div>
      <div class="flex-auto p-6 h-1/2 max-h-screen">
        <div class="relative flex flex-col justify-center">
          <div class="absolute left-6 h-full border-r-2"></div>
          <!-- status and order tracking  -->
          <div class="relative mb-4" v-for="order in orderTracking.slice().sort((a, b) => a.id - b.id)" :key="order.id">
            <span class="absolute inline-flex bg-gray-100 w-12 h-12 rounded-full justify-center items-center">
              <Icon :icon="iconClass(order.status)" width="38" height="38" class="p-1"
                :class="statusClass(order.status)" :ssr="true" />
            </span>
            <div class="ml-16 w-auto pt-1">
              <h6 class="text-sm font-semibold text-blue-900">{{ order.text }}</h6>
              <p class="mt-1 text-xs text-gray-500"><span>{{ order.status }}: </span> {{ order.date }} </p>
            </div>
          </div>
        </div>
      </div>
    </div>


    <NuxtLink to="/history" class="btn btn-primary w-full flex justify-items-end">
      Return
    </NuxtLink>
  </div>
</template>

<script lang="ts" setup>

const { id } = useRoute().params

definePageMeta({
  layout: "custom",
});

const orderTracking = [
  {
    id: 4,
    text: 'Item is recieved by the customer',
    status: 'Delivered',
    date: '22 DEC 7:20 PM',
    total: '$200.00'
  },
  {
    id: 2,
    text: 'Order is being processed',
    status: 'Processing',
    date: '22 DEC 7:20 PM',
    total: '$200.00'
  },
  {
    id: 3,
    text: 'Order is out for delivery',
    status: 'On Delivery',
    date: '22 DEC 7:20 PM',
    total: '$200.00'
  },
  {
    id: 1,
    text: 'Order Placed Successfully',
    status: 'Order Placed',
    date: '22 DEC 7:20 PM',
    total: '$200.00'
  }
]


const statusClass = (status: string) => {
  switch (status) {
    case 'Delivered':
      return 'text-green-500';
    case 'Processing':
      return 'text-blue-500';
    case 'Cancelled':
      return 'text-red-500';
    case 'On Delivery':
      return 'text-yellow-500';
    case 'Order Placed':
      return 'text-green-500';

    default:
      return 'text-gray-500';
  }
}

const iconClass = (icon: string) => {
  switch (icon) {
    case 'Order Placed':
      return 'lets-icons:check-fill';
    case 'Processing':
      return 'line-md:loading-twotone-loop';
    case 'Cancelled':
      return 'ix:cancel';
    case 'On Delivery':
      return 'icon-park-outline:up-c';
    case 'Delivered':
      return 'material-symbols-light:view-in-ar';
    default:
      return 'material-symbols:info';
  }
}

</script>

<style></style>