<template>
  <div>

    <!-- <p class="font-bold text-xl text-[#1455ac] mb-6">Categories</p> -->
    <!-- Categories Grid -->
    <div
      class=" border-gray-200 flex  overflow-x-auto pb-4 -mt-[35px] transition-shadow">
      <div v-for="(item, index) in categories" :key="index"
        class="category-item flex-shrink-0 w-25   rounded-lg  duration-300">
        <div class="p-4">
          <!-- Category Image -->
            <NuxtLink :to="'/categories/' + item.id">
         <!-- <div
            class="category-img-box bg-gray-100 border border-gray-200 rounded-full w-16 h-16 mx-auto flex items-center justify-center">
            <img :src="item.image_full_url" :alt="item.name" class="w-8 h-8 object-contain" />
          </div> -->
         
          <!-- Category Name -->
  <h3 class="category-item-title flex items-center justify-center gap-3 bg-gradient-to-br from-red-50 to-red-100 text-sm font-semibold border border-red-200 rounded-xl text-red-700 text-center py-3 px-6 shadow-xs hover:from-red-100 hover:to-red-200 hover:border-red-300 hover:shadow-sm transition-all duration-200 ease-in-out cursor-pointer">
    <img :src="item.image_full_url" :alt="item.name" class="w-6 h-6 object-contain" />
    <span>{{ item.name }}</span>
</h3>
   </NuxtLink>

          <!-- Show All Button -->
          <!-- <a
            href="#"
            class="category-btn block text-xs font-medium text-pink-500 text-center mt-2 hover:text-pink-600 transition-colors duration-300"
          >
            Show All
          </a> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { useResolveImage } from '~/composable/useResolveImage';

export default {
  data() {
    return {
      categories: [],
      resolveImage: useResolveImage
    };
  },
  mounted() {
    this.categoriesList();
  },
  methods: {
    async categoriesList() {
      try {
        const { apiUrl, apiPrefix,moduleId,zoneId } = useRuntimeConfig().public;
        const response = await axios.get(`${apiUrl}${apiPrefix}/categories`, {
          headers: {
            moduleId: `${moduleId}`, // Replace with your actual moduleId
            zoneId: `${zoneId}`, // Replace with your actual zoneId
          },
        });
        this.categories = response.data; // Adjust if the data structure is different
        console.log('Categories:', this.categories);
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    },
  },
};
</script>

<style>
/* Custom Scrollbar Styles */
.category-item-container::-webkit-scrollbar {
  height: 8px;
}

.category-item-container::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 4px;
}

.category-item-container::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.category-item-container::-webkit-scrollbar-track {
  background: #edf2f7;
  border-radius: 4px;
}
</style>