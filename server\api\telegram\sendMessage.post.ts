import { Telegraf } from "telegraf";
import { formatReceipt } from "~/server/utils/orderService/orderReceipt";

const runtimeConfig = useRuntimeConfig();

export default defineEventHandler(async (event) => {
  const { chatid, order } = await readBody(event);

  const makeReceipt = { id: chatid, order: JSON.parse(order) };
  console.warn("Telegram Making Receipt Request ");

  const telegramToken = runtimeConfig.telegramToken;
  const bot = new Telegraf(telegramToken);

  // Format Receipt
  const receipt = formatReceipt(makeReceipt);

  try {
    await bot.telegram.sendMessage(
      chatid,
      `Your Order has been placed! \n
      ${receipt}`,
      { parse_mode: "Markdown" }
    );

    console.warn("Successfully send to Telegram to chatid: ", chatid);
    return {
      status: 200,
      message: "Successfully send to Telegram " + chatid,
    };
  } catch (error) {
    console.error("Failed to send to Telegram to ChatID: ", chatid, error);
    return {
      status: 500,
      message: "Internal Error" + error,
    };
  }
});
