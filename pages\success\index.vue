<template>
  <transition name="fade">
    <div v-if="showSuccess" class="fixed inset-0 z-50 flex items-center justify-center bg-black/30">
      <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-xl transform transition-all duration-300" 
           :class="{'scale-105': animate}">
        
        <!-- Animated Checkmark -->
        <div class="flex justify-center mb-6">
          <svg class="w-20 h-20 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        
        <!-- Success Message -->
        <div class="text-center">
          <h2 class="text-2xl font-bold text-gray-800 mb-2">Order Confirmed!</h2>
          <!-- <p class="text-gray-600 mb-6">Your order #{{ orderId }} has been placed successfully.</p> -->
          <p class="text-gray-600 mb-6">Your order  has been placed successfully.</p>

          
          <!-- Order Summary -->
        <!--  <div class="bg-gray-50 rounded-lg p-4 mb-6 text-left">
            <div class="flex justify-between mb-2">
              <span class="text-gray-600">Amount:</span>
              <span class="font-medium">${{ orderAmount }}</span>
            </div>
            <div class="flex justify-between mb-2">
              <span class="text-gray-600">Payment:</span>
              <span class="font-medium capitalize">{{ paymentMethod }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Delivery to:</span>
              <span class="font-medium text-right">{{ deliveryAddress }}</span>
            </div>
          </div> -->
          
          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-3">
            <button @click="trackOrder" 
                    class="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-3 px-6 rounded-lg font-medium transition">
              Track Order
            </button>
            <NuxtLink to="/">
            <button @click="closeModal" 
                    class="flex-1 border border-gray-300 hover:bg-gray-50 text-gray-700 py-3 px-6 rounded-lg font-medium transition">
              Continue Shopping
            </button>
            </NuxtLink>
          </div>
        </div>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { ref, onMounted } from 'vue';

const props = defineProps({
  orderId: String,
  orderAmount: Number,
  paymentMethod: String,
  deliveryAddress: String
});
definePageMeta({
layout:"custom"
});
const showSuccess = ref(false);
const animate = ref(false);

const emit = defineEmits(['track-order', 'close']);

onMounted(() => {
  showSuccess.value = true;
  setTimeout(() => { animate.value = true }, 100);
  setTimeout(() => { animate.value = false }, 300);
});

function trackOrder() {
  emit('track-order');
}

function closeModal() {
  showSuccess.value = false;
  setTimeout(() => emit('close'), 300); // Wait for animation
}
</script>

<style scoped>
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* Pulse animation for the checkmark */
@keyframes pulse {
  0% { transform: scale(0.95); opacity: 0.6; }
  50% { transform: scale(1.05); opacity: 1; }
  100% { transform: scale(1); opacity: 1; }
}

svg {
  animation: pulse 1s ease-in-out;
}
</style>