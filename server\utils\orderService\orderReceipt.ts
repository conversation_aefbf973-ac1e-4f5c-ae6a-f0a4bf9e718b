import { PrismaClient } from "@prisma/client";

const runtimeConfig = useRuntimeConfig();
const prisma = new PrismaClient();
const endpoint = runtimeConfig.endpoint + "/api/v1/customer/order/details";

interface OrderRes {
  id: number;
  item_id: number;
  price: number;
  items_detail: [];
}

export async function createOrderRecord(orderId: number, chatid: number) {
  try {
    const user = await prisma.customer.findUnique({
      where: {
        chatid: chatid,
      },
      select: {
        user_token: true,
      },
    });

    if (!user) {
      return {
        status: 404,
        message: "User not found, Can't find order",
      };
    }
    try {
      const getOrder = await $fetch(endpoint, {
        method: "GET",
        headers: {
          Authorization: `Bearer ${user.user_token}`,
          moduleId: "1",
        },
        query: {
          order_id: orderId,
        },
      });

      if (!getOrder) {
        return {
          status: 404,
          message: "Order not found",
        };
      }

      console.info("Get Order Detail:", getOrder.length);
      const totalSumAmount = getOrder.reduce(
        (sum, item) =>
          (sum + item.price - item.discount_on_item) * item.quantity,
        0
      );
      const subTotal = getOrder.reduce(
        (sum, item) => sum + item.price * item.quantity,
        0
      );
      const discount = getOrder.reduce(
        (sum, item) => sum + item.discount_on_item,
        0
      );

      const quantity = getOrder.reduce((sum, item) => sum + item.quantity, 0);
      //   const totalSumAmount = getOrder[0].item_details[0].price;
      const delivery_fee = 2;
      const vat_fee = getOrder.tax;
      try {
        const item_detail = getOrder.map((item) => ({
          name: item.item_details.name,
          detail: item.item_details.description,
          price: item.price,
          quantity: item.quantity,
        }));

        const param = {
          ordered_items: item_detail.length,
          items: item_detail,
          discount: discount * quantity,
          sub_total: subTotal,
          status: "Order Placed",
          ccy: "$",
          customer_id: chatid,
          delivery_fee: delivery_fee,
          order_id: orderId,
          totalAmount: totalSumAmount + delivery_fee,
          vat: vat_fee,
        };
        // console.warn("Example For DB: ", param);
        return {
          message: "Got Receipt",
          data: param,
        };
      } catch (error) {
        console.error("Error Writting to DB", error);
      }
      return {
        message: "Something went wrong",
      };
    } catch (error) {
      console.error("Error while fetching order details", error);

      return {
        message: "External Error: " + error,
      };
    }
  } catch (error) {
    console.error("Error while creating order record", error);
    return {
      message: "Internal Error: " + error,
    };
  }
}

export function formatReceipt(orderData: any): string {
  // Extract order data - handle both old and new structure
  const order = orderData.order || orderData;

  let receipt = `
📄 *Order Receipt*

Order ID: ${order.order_id}
Customer: ${order.name || "N/A"}
Phone: ${order.phone || "N/A"}
Status: Order Placed
------------
### Ordered Items
1. ${order.product} - ${order.quantity} x $${order.price}
------------
Subtotal: $${order.price}
Delivery Fee: $${order.shipping}
Discount: -$${order.discount}
Total Amount: $${order.total}
------------
### Delivery Information
Address: ${order.address}
Payment Method: ${order.paymethod}
****************
🙏!THANK YOU!🙏
****************
`;

  return receipt;
}
