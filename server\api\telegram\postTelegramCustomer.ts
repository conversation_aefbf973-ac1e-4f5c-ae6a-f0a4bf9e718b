import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export default defineEventHandler(async (event) => {
  if (event.node.req.method === "POST") {
    try {
      const body = await readBody(event);
      console.warn("Received Body from Telegram:Post--> ", body);

      const checkCustomer = await prisma.customer.findUnique({
        where: {
          chatid: String(body.id),
        },
      });

      // Call API to create Customer
      try {
        const chatid = String(body.id);

        if (checkCustomer) {

          const user = await prisma.customer.findUnique({
            where: {
              chatid: String(body.id)
            },
            select:{
              name: true,
              phone: true,
              username: true,
            }
          })
          
          return {
            status: 404,
            message: "User Already exist",
            data: user,
          };
        } else {
          console.log("New Customer Found. Creating user....");
          // Create Customer on DB
          const customer = await prisma.customer.create({
            data: {
              chatid: String(body.id),
              phone: body.phone,
              name: body.name,
              lang: body.lang,
              allow_chatbot: body.allow_write_to_psm ?? false,
              photo_url: body.photo_url,
              username: body.username,
              user_token: "",
              hash_pw: "",
            },
          });
          console.log("Customer:" + body.name + " Created Successfully");

          const signUpRes = await $fetch(`/api/telegram/customer/${chatid}`);
          if (signUpRes && signUpRes.status !== 200) {
            console.error("failed to sign up customer	:", signUpRes.message);
            return {
              status: signUpRes.status,
              message: signUpRes.message,
            };
          }

          return {
            status: 200,
            message: "ok",
            data: [],
          };
        }
      } catch (error) {
        console.error("failed to sign up customer	:", error);
      }
    } catch (error) {
      console.error("failed to fetch body from Telegram:", error);
    }
  }
});
