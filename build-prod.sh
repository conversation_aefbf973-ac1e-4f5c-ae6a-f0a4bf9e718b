#!/bin/bash

# Load variables from .env.prod
set -a
source .env.prod
set +a

# Build Docker image with build args
docker buildx build . \
  --build-arg TELEGRAM_BOT_TOKEN="$TELEGRAM_BOT_TOKEN" \
  --build-arg STORE_URL="$STORE_URL" \
  --build-arg API_URL="$API_URL" \
  --build-arg API_PREFIX="$API_PREFIX" \
  --build-arg MERCHANT_PORTAL_ENDPOINT="$MERCHANT_PORTAL_ENDPOINT" \
  --build-arg STORE_ID="$STORE_ID" \
  --build-arg MODULE_ID="$MODULE_ID" \
  --build-arg ZONE_ID="$ZONE_ID" \
  --build-arg DATABASE_URL="$DATABASE_URL" \
  -t hub.orbit-dev.net:4443/social_commerce_webapp:betav12.5


