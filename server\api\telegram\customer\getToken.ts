import { PrismaClient } from "@prisma/client";
import { loginService } from "#imports";

const prisma = new PrismaClient();

export default defineEventHandler(async (event) => {
  if (event.node.req.method === "POST") {
    const body = await readBody(event);

    console.warn("Request to get token:", body.id);

    // Get login Token
    const checkToken = await prisma.customer.findUnique({
      where: {
        chatid: String(body.id),
      },
      select: {
        user_token: true,
        username: true,
        phone: true,
      },
    });

    if (!checkToken) {
      return {
        status: 404,
        message: "Token Not Found",
        data: [],
      };
    }

    // If token exists but we want to ensure it's fresh, call loginService
    // This ensures the token in DB and the one we return are the same
    try {
      const loginRes = await loginService(String(body.id));

      if (loginRes.status === 200 && loginRes.data) {
        // Return the fresh token
        return {
          status: 200,
          message: "Token Refreshed",
          data: loginRes.data, // This is the fresh token that was also saved to DB
          profile: { name: checkToken.username, phone: checkToken.phone },
        };
      }
    } catch (error) {
      console.error("Error refreshing token:", error);
      // If refresh fails, fall back to the stored token
    }

    // Return the stored token if refresh failed or wasn't needed
    return {
      status: 200,
      message: "Token Found",
      data: checkToken.user_token,
      profile: { name: checkToken.username, phone: checkToken.phone },
    };
  }
});
