
generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "windows", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

model Customer {
  id            Int     @id @default(autoincrement())
  chatid        String  @unique
  name          String?
  phone         String
  username      String
  lang          String
  allow_chatbot Boolean
  user_token    String  @db.VarChar(2048)
  hash_pw       String
  photo_url     String
  chats         Chat[]
}

model Botinfo {
  id                         Int    @id @default(autoincrement())
  bot_name                   String
  bot_notify_to_id           BigInt
  bot_notify_to_group_name   String
  bot_notify_group_privilige String
}

model Chat {
  id        Int      @id @default(autoincrement())
  chatid    String
  customer  Customer @relation(fields: [chatid], references: [chatid], map: "fk_chat_customer_chatid")
  senderId  Int?
  type      String
  data      String   @db.LongText
  createAt  DateTime @default(now())
  updatedAt DateTime @updatedAt

  /// explicit index on chatid so Prisma uses `idx_chat_chatid` instead of auto-generated names
  @@index([chatid], map: "idx_chat_chatid")
}
