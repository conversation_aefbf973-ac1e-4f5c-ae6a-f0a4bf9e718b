<template>

  <form @submit.prevent="submitForm" class="max-w-lg mx-auto">
    <div class="bg-white rounded-xl shadow-md p-6 lg:p-8">
      <h2 class="text-2xl font-bold text-gray-800 mb-2">Complete Your Order</h2>
      <p class="text-gray-500 mb-6">Please provide your basic information to proceed</p>

      <div class="space-y-5">
        <!-- Name Field -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
          <input type="text" id="name" placeholder="<PERSON>"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
            v-model="name" />
        </div>

        <!-- Phone Field -->
        <div>
          <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
          <input type="tel" id="phone" placeholder="012 555 888"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
            required v-model="phone" />
        </div>

        <!-- Address Field -->
        <div>
          <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Delivery Address</label>
          <div class="flex gap-2">
            <input type="text" id="address" placeholder="Toulouk, Dakar"
              class="flex-1 px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
              v-model="address" />
            <button @click.prevent="getLocation"
              class="flex items-center justify-center w-12 rounded-lg bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors"
              type="button" aria-label="Use current location">
              <Icon icon="map:postal-code" class="w-5 h-5" />
            </button>
          </div>
        </div>
        <!-- Payment Method -->
        <div class="pt-2">
          <p class="block text-sm font-medium text-gray-700 mb-3">Payment Method</p>
          <div class="space-y-3">
            <div class="flex items-center p-3 border rounded-lg cursor-pointer hover:border-blue-400 transition-colors"
              :class="{ 'border-blue-500 bg-blue-50': paymethod === 'KHQR Payment' }"
              @click="paymethod = 'KHQR Payment'">
              <input type="radio" id="khqr" name="payment" v-model="paymethod" value="KHQR Payment"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500" />
              <label for="khqr" class="ml-3 block text-sm font-medium text-gray-700">KHQR Payment</label>
            </div>

            <div class="flex items-center p-3 border rounded-lg cursor-pointer hover:border-blue-400 transition-colors"
              :class="{ 'border-blue-500 bg-blue-50': paymethod === 'cash_on_delivery' }"
              @click="paymethod = 'cash_on_delivery'">
              <input type="radio" id="cod" name="payment" v-model="paymethod" value="cash_on_delivery" checked
                class="h-4 w-4 text-blue-600 focus:ring-blue-500" />
              <label for="cod" class="ml-3 block text-sm font-medium text-gray-700">Cash on Delivery</label>
            </div>
          </div>
        </div>

        <!-- Submit Button -->
        <!-- <NuxtLink to="/success"> -->
        <button type="submit" class="w-full mt-6 py-3 px-4 btn btn-primary">
          Confirmed Order
        </button>
        <!-- </NuxtLink> -->
      </div>
    </div>
  </form>
</template>

<script setup>
import { ref, computed, onMounted } from "vue";
import axios from "axios";
import { useAuth } from '~/composable/useAuth';

definePageMeta({
  layout: "custom",
});

const name = ref("");
const phone = ref("");
const address = ref("");

// Use the auth composable
const auth = useAuth();

// For backward compatibility and template binding
const getUserToken = computed(() => auth.token.value);
const getUserChatId = computed(() => auth.chatId.value);

const userstaus = ref();
const paymethod = ref("cash_on_delivery");

const props = defineProps({
  total: {
    type: Number,
    required: true,
  },
  shipping: {
    type: Number,
    required: true,
  },
  price: {
    type: Number,
    required: true,
  },
  product: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  phone: {
    type: String,
    required: true,
  },
  image: {
    type: String,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  variation: {
    type: Number,
    required: true,
  },

  quantity: {
    type: Number,
    required: true,
  },
  discount: {
    type: String,
    required: true,
  },
});

const formatNumber = computed(() => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(props.total);
});

// Fetch user info and populate fields
onMounted(async () => {
  // Initialize auth if not already initialized
  if (!auth.isAuthenticated.value) {
    await auth.initialize();
  }

  // Update profile data if available
  if (auth.userProfile.value) {
    phone.value = auth.userProfile.value.phone || "None was taken";
    name.value = auth.userProfile.value.name || "";
  }
});

const long = ref(0);
const lat = ref(0);
const apiKey = 'AIzaSyCmIZXqX8-6P9kC9f6XTUB6cvEACU51DCU'; // Replace with your actual Google Maps API key

async function getLocation() {
  try {
    // Step 1: Get latitude and longitude from getLocationTelegram()
    const { longitude, latitude } = await getLocationTelegram();

    // Step 2: Update the longitude and latitude values
    long.value = longitude;
    lat.value = latitude;

    // Step 3: Fetch the address using the Google Maps Geocoding API
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`;
    const response = await axios.get(url);

    // Step 4: Check if the response contains valid address data
    if (response.data.results.length > 0) {
      address.value = response.data.results[0].formatted_address;
    } else {
      address.value = 'Address not found';
    }
  } catch (error) {
    // Handle errors
    console.error('Error fetching location or address:', error);
    address.value = 'Error fetching address';
  }
}


function submitForm() {
  if (!name.value) name.value = "Unknown";
  if (!address.value) address.value = "Contact for address";

  const { apiUrl, apiPrefix } = useRuntimeConfig().public;
  addItemToCart();
  navigateTo(`success`);

  // Step 1: Check if the phone number exists
}

async function addItemToCart() {
  const { apiUrl, moduleId, apiPrefix } = useRuntimeConfig().public;
  const cartData = {
    order_amount: 200,
    //cash_on_delivery
    //payment_method: orderData.paymethod,
    payment_method: "cash_on_delivery",
    order_type: "delivery",
    store_id: 2, // Replace with actual store ID
    distance: 2, // Replace with actual distance
    address: address.value,
    longitude: 104.865144,
    latitude: 11.562300,
  };
  console.log("cartData", cartData);
  const cartUri = `${apiUrl}${apiPrefix}/customer/order/place`;


  try {
    await axios.post(cartUri, null, {
      params: cartData,
      headers: {
        moduleId: `${moduleId}`,
        Authorization: auth.token.value ? `Bearer ${auth.token.value}` : '',
      },
    });
    console.log("Item added to cart successfully.");
  } catch (error) {
    console.error("Error adding item to cart:", error.message);
  }
}


</script>

<style>
/* Add your custom styles here */
</style>
