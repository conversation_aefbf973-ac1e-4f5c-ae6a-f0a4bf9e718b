import { createOrderRecord } from "~/server/utils/orderService/orderReceipt";

export default defineEventHandler(async (event) => {
  const { orderId, chatid } = getQuery(event);

  const getOrder = await createOrderRecord(Number(orderId), Number(chatid));

  // Send to Telegram as Receipt
  const body_params = {
    chatid: chatid,
    order: JSON.stringify(getOrder),
  };

  // console.warn("Order: ", body_params.order);

  const telegramPush = await $fetch("/api/telegram/sendMessage", {
    method: "POST",
    body: body_params,
  });

  if (!telegramPush) {
    return {
      status: 404,
      message: "Failed to send to Telegram",
    };
  }
  // console.info("Sending Order to Telegram: ", body_params);

  return {
    status: 200,
    message: getOrder.message,
    data: getOrder.data,
  };
});
