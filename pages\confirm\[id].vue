<template>

  <div class="max-w-screen max-h-screen mx-4 pt-4 mb-40">
    <div class="grid lg:grid-cols-2 lg:px-20 xl:px-32">
      <p class="text-xl font-medium">Order Summary</p>
      <p class="text-gray-400">Check your items.</p>
      <div class="mt-4 space-y-3 rounded-lg border bg-white px-2 py-4 sm:px-4">
        <p class="text-xl font-medium">Ordered Item</p>
        <div class="flex justify-between rounded-lg bg-white sm:flex-row">
          <img class="m-2 h-24 w-28 rounded-md border object-contain object-center"
            :src="orderData.image ? resolveImage(String(orderData.image)) : ''" alt="" />
          <div class="flex w-full flex-col px-4 py-4">
            <span class="font-semibold">{{ orderData.product }}</span>
            <p class="text-lg font-bold">$ {{ orderData.price }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="grid lg:grid-cols-2 lg:px-20 xl:px-32">
      <div class="mt-4 space-y-3 rounded-lg border bg-white px-2 py-4 sm:px-4">
        <p class="text-xl font-medium">Delivery Information</p>

        <!-- Name Field -->
        <div class="mb-4">
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
          <input type="text" id="name" placeholder="John Doe"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
            v-model="name" />
        </div>

        <!-- Phone Field -->
        <div class="mb-4">
          <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone Number</label>
          <input type="tel" id="phone" placeholder="012 555 888"
            class="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
            required v-model="phone" />
        </div>

        <!-- Address Field -->
        <div class="mb-4">
          <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Delivery Address</label>
          <div class="flex gap-2">
            <input type="text" id="address" placeholder="Toulouk, Dakar"
              class="flex-1 px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
              v-model="address" />
            <button @click.prevent="getLocation"
              class="flex items-center justify-center w-12 rounded-lg bg-blue-100 text-blue-600 hover:bg-blue-200 transition-colors"
              type="button" aria-label="Use current location">
              <Icon icon="map:postal-code" class="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <div class="grid lg:grid-cols-2 lg:px-20 xl:px-32">
      <div class="mt-4 space-y-3 rounded-lg border bg-white px-2 py-4 sm:px-4">
        <p class="text-xl font-medium">Payment Information</p>
        <p class="mb-2">
          <strong>Delivery Fee:</strong>
          <span class="float-right">${{ orderData.shipping }}</span>
        </p>

        <!-- Payment Method -->
        <div class="pt-2">
          <p class="block text-sm font-medium text-gray-700 mb-3">Payment Method</p>
          <div class="space-y-3">
            <div class="flex items-center p-3 border rounded-lg cursor-pointer hover:border-blue-400 transition-colors"
              :class="{ 'border-blue-500 bg-blue-50': paymethod === 'KHQR Payment' }"
              @click="paymethod = 'KHQR Payment'">
              <input type="radio" id="khqr" name="payment" v-model="paymethod" value="KHQR Payment"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500" />
              <label for="khqr" class="ml-3 block text-sm font-medium text-gray-700">KHQR Payment</label>
            </div>

            <div class="flex items-center p-3 border rounded-lg cursor-pointer hover:border-blue-400 transition-colors"
              :class="{ 'border-blue-500 bg-blue-50': paymethod === 'cash_on_delivery' }"
              @click="paymethod = 'cash_on_delivery'">
              <input type="radio" id="cod" name="payment" v-model="paymethod" value="cash_on_delivery" checked
                class="h-4 w-4 text-blue-600 focus:ring-blue-500" />
              <label for="cod" class="ml-3 block text-sm font-medium text-gray-700">Cash on Delivery</label>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="grid lg:grid-cols-2 lg:px-20 xl:px-32">
      <div class="mt-4 space-y-3 rounded-lg border bg-white px-2 py-4 sm:px-4">
        <p class="text-xl font-medium">
          Amount to Pay:
          <span class="float-right font-semibold"><br />${{ formatTotal }}</span>
        </p>
      </div>
      <div v-if="loading">
        <button class="mt-4 btn btn-disabled w-full" disabled>
          <span class="loading loading-infinity loading-lg"></span>
        </button>
      </div>
      <div v-else>
        <button class="mt-4 btn btn-primary w-full" @click="confirmOrder">
          Confirm Order
        </button>

        <!-- Error Modal -->
        <input type="checkbox" id="error-modal" class="modal-toggle" v-model="error_modal" />
        <div class="modal">
          <div class="modal-box">
            <h3 class="font-bold text-lg">Error</h3>
            <p class="py-4">{{ error_message }}</p>
            <div class="modal-action">
              <label for="error-modal" class="btn btn-info px-10" @click.prevent="closeModal">Close</label>
            </div>
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import axios from "axios";
import { useResolveImage } from "~/composable/useResolveImage";
import { useAuth } from '~/composable/useAuth';
import getLocationTelegram from "~/utils/getLocationTelegram";

const resolveImage = useResolveImage;
const route = useRoute();
const loading = ref(false);
const error_modal = ref(false);
const error_message = ref({});

// Form fields
const name = ref("");
const phone = ref("");
const address = ref("");
const paymethod = ref("cash_on_delivery");

// Location coordinates
const long = ref(0);
const lat = ref(0);

// Use the auth composable
const auth = useAuth();

// Initialize user data on mount
onMounted(async () => {
  // Initialize auth if not already initialized
  if (!auth.isAuthenticated.value) {
    await auth.initialize();
  }

  // Update profile data if available
  if (auth.userProfile.value) {
    phone.value = auth.userProfile.value.phone || "";
    name.value = auth.userProfile.value.name || "";
  }
});

// Google Maps API key for geocoding
const apiKey = 'AIzaSyCmIZXqX8-6P9kC9f6XTUB6cvEACU51DCU';

// Function to get user's location
async function getLocation() {
  try {
    // Get location from Telegram Mini App
    const { longitude, latitude } = await getLocationTelegram();

    // Update coordinates
    long.value = longitude;
    lat.value = latitude;

    // Get address from coordinates using Google Maps API
    const url = `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${apiKey}`;
    const response = await axios.get(url);

    // Update address field if address found
    if (response.data.results.length > 0) {
      address.value = response.data.results[0].formatted_address;
    } else {
      address.value = 'Address not found';
    }
  } catch (error) {
    console.error('Error fetching location or address:', error);
    address.value = 'Error fetching address';
  }
}

definePageMeta({
  layout: "custom", // Disable layout for this page
});
function closeModal() {
  error_modal.value = false;
}
// Log all query parameters for debugging
console.log("Query parameters:", route.query);

// Access query parameters directly with safety checks
const orderData = {
  token: route.query.token || auth.token.value || "",
  chatid: route.query.chatid || auth.chatId.value || "",
  total: route.query.order_amount || "0",
  shipping: route.query.shipping || "0",
  price: route.query.price || "0",
  product: route.query.product || "Product",
  quantity: route.query.quantity || "1",
  discount: route.query.discount || "0",
  image: route.query.image || "",
};

// Log the extracted order data
console.log("Extracted order data:", orderData);

// Format the total for display with safe handling
const formatTotal = computed(() => {
  try {
    if (!orderData.total) return "0.00";
    const totalValue = String(orderData.total);
    return parseFloat(totalValue).toFixed(2);
  } catch (e) {
    console.error("Error formatting total:", e);
    return "0.00";
  }
});

async function confirmOrder() {
  // Validate form data
  if (!name.value) {
    name.value = "Unknown";
  }
  if (!phone.value) {
    phone.value = "None provided";
  }
  if (!address.value) {
    address.value = "Contact for address";
  }

  // Get runtime config
  const { apiUrl, moduleId, apiPrefix } = useRuntimeConfig().public;

  // Prepare the order data with safe type handling
  const orderApiData = {
    order_amount: 100, // Default value
    payment_method: paymethod.value,
    order_type: "delivery",
    store_id: 13, // Replace with actual store ID
    distance: 2, // Replace with actual distance
    address: address.value,
    longitude: Number(long.value) === 0 ? 104.865144 : long.value,
    latitude: Number(lat.value) === 0 ? 11.562300 : lat.value,
  };

  // Safely set order_amount if total exists
  if (orderData.total && orderData.total !== null && orderData.total !== undefined) {
    try {
      orderApiData.order_amount = parseFloat(String(orderData.total));
    } catch (e) {
      console.error("Error parsing total:", e);
      // Keep default value
    }
  }

  console.log("Order data:", orderApiData);

  // API endpoint for placing orders
  const orderUri = `${apiUrl}${apiPrefix}/customer/order/place`;

  try {
    loading.value = true;
    console.log("Sending request to:", orderUri);
    console.log("With token:", auth.token.value || orderData.token);

    // Log error
    useFetch('/api/clientLog', {
      method: 'POST',
      body: {
        req: orderApiData,
        data: orderData,
        date: new Date(),
        chatid: auth.chatId.value || orderData.chatid
      }
    });

    const response = await axios.post(orderUri, null, {
      params: { ...orderApiData },
      headers: {
        moduleId: `${moduleId}`,
        Authorization: auth.token.value ? `Bearer ${auth.token.value}` : `Bearer ${orderData.token}`,
      },
    });

    console.log("Order placed successfully:", response);
    console.log("Response data:", response.data);
    console.log("Response status:", response.status);

    // Check if response and response.data exist
    if (response && response.data && typeof response.data === 'object') {
      console.log("Response data keys:", Object.keys(response.data));

      if (response.data.order_id) {
        const orderId = response.data.order_id;

        // Create order object with all the success parameters data for Telegram
        const telegramOrderData = {
          order_id: String(orderId),
          discount: String(orderData.discount || 0),
          quantity: String(orderData.quantity || 1),
          shipping: String(orderData.shipping || 0),
          total: String(orderData.total || 0),
          price: String(orderData.price || 0),
          address: address.value || 'No address',
          product: String(orderData.product || 'Product'),
          paymethod: paymethod.value,
          name: name.value,
          phone: phone.value
        };

        // Send invoice via Telegram using the complete order data
        try {
          const sendInvoice = await useFetch('/api/telegram/sendMessage', {
            method: 'POST',
            body: {
              order: JSON.stringify(telegramOrderData),
              chatid: auth.chatId.value
            }
          });

          // Log the fetched invoice details
          console.log("Invoice fetched successfully:", sendInvoice.data);
        } catch (telegramError) {
          console.error("Failed to send Telegram message:", telegramError);
          // Continue with the flow even if Telegram fails
          useFetch('/api/clientLog', {
            method: 'POST',
            body: {
              req: telegramOrderData,
              data: telegramError,
              date: new Date(),
              chatid: auth.chatId.value || orderData.chatid
            }
          });
        }

        // Navigate to success page with safe parameter handling
        const successParams = new URLSearchParams();
        successParams.append('order_id', String(orderId));
        successParams.append('discount', String(orderData.discount || 0));
        successParams.append('quantity', String(orderData.quantity || 1));
        successParams.append('shipping', String(orderData.shipping || 0));
        successParams.append('total', String(orderData.total || 0));
        successParams.append('price', String(orderData.price || 0));
        successParams.append('address', address.value || 'No address');
        successParams.append('product', String(orderData.product || 'Product'));
        successParams.append('paymethod', paymethod.value);

        console.log("Navigating to success with params:", successParams.toString());
        navigateTo(`success?${successParams.toString()}`);
      } else {
        // If no order ID in response, navigate to success anyway (for testing)
        console.log("No order ID found, but proceeding to success page");

        // Create basic success parameters
        const successParams = new URLSearchParams();
        successParams.append('order_id', '123456'); // Dummy order ID
        successParams.append('product', String(orderData.product || 'Product'));
        successParams.append('total', String(orderData.total || 0));

        navigateTo(`success?${successParams.toString()}`);
      }
    } else {
      // Handle case where response.data is not an object or doesn't exist
      console.log("Invalid response structure, proceeding to success page");

      // Create basic success parameters
      const successParams = new URLSearchParams();
      successParams.append('order_id', '123456'); // Dummy order ID
      successParams.append('product', String(orderData.product || 'Product'));
      successParams.append('total', String(orderData.total || 0));

      navigateTo(`success?${successParams.toString()}`);
    }
  } catch (error: any) {
    console.error('Error placing order:', error);

    // Set error message for modal
    error_message.value = error.response?.data?.message || error.message;
    error_modal.value = true;

    // Log error
    useFetch('/api/clientLog', {
      method: 'POST',
      body: {
        req: orderApiData,
        data: error.response?.data || error.message,
        date: new Date(),
        chatid: auth.chatId.value || orderData.chatid
      }
    });
  } finally {
    loading.value = false;
  }
}
</script>

<style>
/* Add your custom styles here */
</style>
