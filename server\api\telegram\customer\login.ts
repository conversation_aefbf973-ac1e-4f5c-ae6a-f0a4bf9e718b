import { loginService } from "#imports";

// const runtimeConfig = useRuntimeConfig();
// const prisma = new PrismaClient();

// const endpoint = runtimeConfig.endpoint + "auth/login";

interface LoginResponse {
  status: number;
  message: string;
  data: string | object;
}

export default defineEventHandler(async (event) => {
  if (event.node.req.method === "POST") {
    try {
      const body = await readBody(event);
      if (!body.id) {
        return {
          status: 404,
          message: "Mandatory Field is missing",
        };
      }
      console.warn("Received Login Customer Body from Telegram:Post--> ", body);

      const loginRes = (await loginService(body.id)) as LoginResponse;

      if (loginRes.status === 200) {
        return {
          status: 200,
          message: "ok",
          data: loginRes.data,
        };
      }
    } catch (error) {
      console.error(error);
      return {
        status: 500,
        message: "Internal Error" + error,
      };
    }
  }
});
