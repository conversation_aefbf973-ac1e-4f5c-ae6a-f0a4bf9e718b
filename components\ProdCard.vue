<template>
  <div>
    <p class="font-bold bg-orange-300 text-lg text-center font-san text-black">
      All Products
    </p>
  </div>
  <div class="grid w-full grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 px-2 py-2">
    <div v-for="product in products" :key="product.id"
      class="relative mt-4 w-full overflow-hidden rounded-lg bg-white shadow-md">
      <NuxtLink :to="'/products/' + product.id">
        <figure class="figure justify-center">
          <img class="h-32 w-32 rounded-t-lg object-contain mx-auto" :src="useResolveImage(product.image_full_url)"
            alt="product image" />
        </figure>
      </NuxtLink>

      <span
        class="absolute top-2 left-2 bg-gradient-to-br from-red-500 to-rose-600 text-white text-xs font-extrabold uppercase tracking-wider px-3 py-1 rounded-full shadow-lg flex items-center justify-center z-10 transform rotate-0 hover:rotate-[-4deg] transition-transform"
        v-show="product.discount !== 0">-{{ product.discount
        }}{{ product.discount_type === "percent" ? "%" : "$" }} OFF
        <div></div>
      </span>
      <div class="card-body mt-4 px-5 pb-5">
        <a href="#">
          <h5 class="text-md text-center tracking-tight text-slate-700 truncate font-bold whitespace-nowrap">
            {{ product.name }}
          </h5>
        </a>
        <div class="flex text-center items-center justify-between">
          <p>
            <span v-if="product.discount !== 0" class="text-xl text-center font-semibold">
              <span class="text-xl text-center font-semibold" :class="{
                'line-through text-gray-500': product.discount !== 0,
              }">
                ${{ product.price.toFixed(2) }}
              </span>
              <span class="text-green-500">
                ${{
                  (product.discount_type === "percent"
                    ? product.price - (product.price * product.discount) / 100
                    : product.price - product.discount
                  ).toFixed(2)
                }}
              </span>
            </span>

            <!-- Show Original Price if No Discount Exists -->
            <span v-else class="text-xl text-green-500 text-center font-semibold">
              ${{ product.price.toFixed(2) }}
            </span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  layout: "custom", // Use the admin layout
};
</script>
<script setup>
import { ref, onMounted } from "vue";
import axios from "axios";
import { useResolveImage } from "~/composable/useResolveImage";

const products = ref([]);
const moduleId = "1";
const { apiUrl, apiPrefix } = useRuntimeConfig().public;
// const addToCart = async (itemId, price) => {
//   const quantity = 1; // Set the quantity as needed
//   const uri = `${apiUrl}{apiPrefix}/customer/cart/add?item_id=${itemId}&model=Item&price=${price}&quantity=${quantity}`;
//   console.log("Add to cart", uri);
//   try {
//     const response = await axios.post(uri, null, {
//       headers: {
//         moduleId: "1",
//         Authorization:
//           "Bearer ********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
//       },
//     });
//     console.log("Item added to cart:", response);
//   } catch (error) {
//     console.error("Error adding item to cart:", error);
//   }
// };

const productList = async () => {
  try {
    const { apiUrl, storeId, moduleId, zoneId, apiPrefix } =
      useRuntimeConfig().public;
    console.log("storeID", storeId);
    const response = await axios.get(`${apiUrl}${apiPrefix}/items/latest`, {
      params: {
        store_id: `${storeId}`,
        category_id: "all",
        limit: 25,
        offset: 1,
      },
      headers: {
        moduleId: `${moduleId}`, // Replace with your actual moduleId
        zoneId: `${zoneId}`, // Replace with your actual zoneId
      },
    });
    console.log("Products", response.data.products);
    // await useFetch('/api/clientLog', {
    //   method: 'POST',
    //   body: {
    //     data: response.data.products,
    //     date: new Date(),
    //     chatid: auth.chatId.value || orderData.chatid
    //   }
    // });
    products.value = response.data.products; // Assuming the response data is the product list
  } catch (error) {
    console.error("Error fetching products:", error);
  }
};

onMounted(() => {
  productList();
});
</script>

<style scoped>
/* Add any additional styles here */
</style>
