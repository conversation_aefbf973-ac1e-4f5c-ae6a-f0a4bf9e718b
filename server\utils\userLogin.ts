import { PrismaClient } from "@prisma/client";

const runtimeConfig = useRuntimeConfig();
const prisma = new PrismaClient();
const endpoint = runtimeConfig.endpoint + "/api/v1/auth/login";

interface LoginResponse {
  token: string;
  password: string;
  chatid: string;
  is_phone_verified: number;
  is_email_verified: number;
  is_personal_info: number;
  is_exist_user: number;
  login_type: number;
  email: number;
}

export async function loginService(chatid: string) {
  try {
    const user = await prisma.customer.findUnique({
      where: {
        chatid: String(chatid),
      },
      select: {
        hash_pw: true,
        chatid: true,
      },
    });

    if (!user) {
      return {
        status: 404,
        message: "User Not Found",
      };
    }

    const params = {
      login_type: "manual",
      email_or_phone: "",
      password: user.hash_pw,
      field_type: "phone",
      chatid: Number(user.chatid),
    };

    const loginRes = await $fetch<LoginResponse>(`${endpoint}`, {
      headers: {
        moduleId: "1",
      },
      method: "POST",
      params,
    });

    if (loginRes.token) {
      console.info("User Sign In Success!");
      console.info(loginRes);
      console.info("Updating Token to DB");
      await prisma.customer.update({
        where: {
          chatid: String(chatid),
        },
        data: {
          user_token: loginRes.token,
        },
      });
      console.info("Update in DB Success");

      return {
        status: 200,
        message: "Login Success",
        data: loginRes.token,
      };
    }
  } catch (error) {
    console.error("login Error:", error);
    return {
      status: 500,
      message: "Internal Error" + error,
    };
  }
}
