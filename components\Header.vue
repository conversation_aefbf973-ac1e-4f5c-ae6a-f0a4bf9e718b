<template>
  <div class="navbar bg-base-100 sticky top-0 z-50">
    <div class="flex-1">
      <NuxtLink to="/" class="btn btn-ghost normal-case text-xl flex items-center">
        <NuxtImg :src="storeImage" :alt="storeList.logo_full_url" class="w-12 max-h-12" />
        <span class="text-neutral ml-2">{{ storeList.name }}</span>
      </NuxtLink>
    </div>
    <!-- Old shopping -->
    <!-- <NuxtLink to="/cart" class="btn btn-ghost btn-circle indicator mr-4">
      <span class="indicator-item badge badge-secondary">{{
        cartList.length
      }}</span>
      <img src="/icon.png" alt="Cart" class="w-8 h-8" />
    </NuxtLink> -->
    <NuxtLink to="/cart" class="btn btn-ghost btn-circle indicator mr-4">
      <span class="indicator-item badge badge-secondary">
        {{ cartList.length }}
      </span>

      <!-- Your custom SVG icon -->
      <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 text-black" fill="none" viewBox="0 0 24 24"
        stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
          d="M3 4h2l1 6h13l1.5-4H6.5M6 16h12M9 20a1 1 0 11-2 0 1 1 0 012 0zm8 0a1 1 0 11-2 0 1 1 0 012 0z" />
      </svg>
    </NuxtLink>
    <!-- <div class="dropdown dropdown-end">
      <label tabindex="0" class="btn btn-ghost btn-circle avatar">
        <div class="w-10 h-10 flex items-center justify-center mt-1">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            class="w-8 h-8 text-gray-700 mt-1"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              d="M12 12c2.7 0 5-2.3 5-5s-2.3-5-5-5-5 2.3-5 5 2.3 5 5 5zm0 2c-3 0-9 1.5-9 4.5V21h18v-2.5c0-3-6-4.5-9-4.5z"
            />
          </svg>
        </div>
      </label>
      <ul
        tabindex="0"
        class="menu menu-sm dropdown-content mt-3 p-2 shadow bg-base-100 rounded-box w-52"
      >
        <li>
          <NuxtLink to="/history">My Orders</NuxtLink>
        </li>
      </ul>
    </div> -->
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from "vue";
import axios from "axios";
import { useResolveImage } from "~/composable/useResolveImage";
import { useAuth } from "~/composable/useAuth";

const storeList = ref({});
const storeImage = ref("");
const cartList = ref([]);

// Use the auth composable
const auth = useAuth();

// For backward compatibility and template binding
const getUserToken = computed(() => auth.token.value);
const getUserChatId = computed(() => auth.chatId.value);

async function getStoreDetails() {
  try {
    const { apiUrl, apiPrefix } = useRuntimeConfig().public;
    const { data } = await axios.get(`${apiUrl}${apiPrefix}/stores/details/13`);
    storeList.value = data;
    storeImage.value = useResolveImage(data.logo_full_url);
  } catch (error) {
    console.error(error);
  }
}

async function getCountCart() {
  try {
    const { apiUrl, moduleId, zoneId, apiPrefix } = useRuntimeConfig().public;
    const { data } = await axios.get(
      `${apiUrl}${apiPrefix}/customer/cart/list`,
      {
        headers: {
          moduleId,
          zoneId,
          Authorization: auth.token.value ? `Bearer ${auth.token.value}` : "",
        },
      }
    );
    cartList.value = data;
  } catch (error) {
    console.error(error);
  }
}

onMounted(async () => {
  // Initialize auth if not already initialized
  if (!auth.isAuthenticated.value) {
    await auth.initialize();
  }

  await getStoreDetails();
  await getCountCart();
});
</script>
