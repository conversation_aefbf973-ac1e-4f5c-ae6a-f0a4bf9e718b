{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "start": "nuxt dev", "db:deploy": "npx prisma migrate deploy && npx prisma generate"}, "dependencies": {"@iconify-json/mdi": "^1.2.3", "@nuxt/image": "^1.9.0", "@nuxtjs/ngrok": "^3.0.1", "@prisma/client": "^6.4.1", "@prisma/nuxt": "^0.3.0", "@telegram-apps/sdk": "^3.4.0", "axios": "^1.7.9", "nuxt": "^3.15.4", "nuxt-icon-tw": "^1.0.0", "prisma": "^6.4.1", "sweetalert2": "^11.17.2", "swiper": "^11.2.2", "tailwindcss": "^3.4.17", "telegraf": "^4.16.3", "vue": "latest", "vue-router": "latest", "zod": "^3.24.4"}, "devDependencies": {"@iconify-json/mdi": "^1.2.3", "@nuxtjs/ngrok": "^3.0.1", "@nuxtjs/tailwindcss": "^6.13.1", "@prisma/client": "^6.4.1", "@prisma/nuxt": "^0.3.0", "@telegram-apps/sdk": "^3.3.0", "daisyui": "^4.12.23", "prettier": "^3.4.2", "prisma": "^6.4.1"}}