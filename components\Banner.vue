<template>
  <div class="mx-auto carousel-container w-full rounded-xl relative overflow-hidden">
    <div class="carousel w-full h-full relative">
      <div v-for="(banner, index) in getBanner" :key="index" :id="'slide' + index"
        class="carousel-item absolute inset-0 w-full max-h-lg flex justify-center items-center transition-all duration-500 ease-in-out"
        :style="{
          transform: `translateX(${(index - currentSlide) * 100}%)`,
          zIndex: currentSlide === index ? 10 : 1
        }">
        <NuxtImg :src="useResolveImage(banner.image_full_url)" :alt="banner.image_full_url"
          class="rounded-box object-cover h-auto max-w-full" loading="lazy" />
      </div>
    </div>

  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import axios from 'axios';
import { useResolveImage } from '~/composable/useResolveImage';

const getBanner = ref([]);
const currentSlide = ref(0);
let slideInterval;

const getBannerList = async () => {
  try {
    const { apiUrl, storeId, zoneId, moduleId, apiPrefix } = useRuntimeConfig().public;
    const response = await axios.get(`${apiUrl}${apiPrefix}/banners`, {
      headers: {
        moduleId: `${moduleId}`,
        zoneId: `${zoneId}`,
      }
    });
    getBanner.value = await response.data.banners;
    startSlider();
  } catch (error) {
    console.error("Error fetching banners:", error);
  }
};

const startSlider = () => {
  if (getBanner.value.length > 1) {
    slideInterval = setInterval(() => {
      nextSlide();
    }, 5000); // 5 seconds interval
  }
};

const stopSlider = () => {
  clearInterval(slideInterval);
};

const nextSlide = () => {
  currentSlide.value = (currentSlide.value + 1) % getBanner.value.length;
};

const prevSlide = () => {
  currentSlide.value = (currentSlide.value - 1 + getBanner.value.length) % getBanner.value.length;
};

const goToSlide = (index) => {
  currentSlide.value = index;
  resetInterval();
};

const resetInterval = () => {
  stopSlider();
  startSlider();
};

onMounted(() => {
  getBannerList();
});

onUnmounted(() => {
  stopSlider();
});
</script>

<style scoped>
.carousel-container {
  aspect-ratio: 16/9;
  /* Adjust this based on your preferred banner ratio */
}

.carousel {
  overflow: hidden;
}

.carousel-item {
  will-change: transform;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .carousel-container {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .carousel-container {
    height: 300px;
  }

  .carousel-item .text-2xl {
    font-size: 1.25rem;
  }

  .carousel-item p {
    font-size: 0.875rem;
  }
}

@media (max-width: 640px) {
  .carousel-container {
    height: 250px;
    aspect-ratio: 3/2;
  }

  .carousel-item .text-2xl {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }

  .carousel-item button {
    padding: 0.5rem 1rem;
    font-size: 0.75rem;
    margin-top: 0.5rem;
  }

  /* Hide navigation buttons on very small screens */
  .carousel-container>button {
    display: none;
  }
}
</style>