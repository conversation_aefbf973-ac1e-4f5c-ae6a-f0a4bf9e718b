-- CreateTable
CREATE TABLE `Customer` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `chatid` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NULL,
    `phone` VARCHAR(191) NOT NULL,
    `username` VA<PERSON><PERSON><PERSON>(191) NOT NULL,
    `lang` VARCHAR(191) NOT NULL,
    `allow_chatbot` BOOLEAN NOT NULL,
    `user_token` VARCHAR(2048) NOT NULL,
    `hash_pw` VARCHAR(191) NOT NULL,
    `photo_url` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `Customer_chatid_key`(`chatid`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Botinfo` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `bot_name` VARCHAR(191) NOT NULL,
    `bot_notify_to_id` BIGINT NOT NULL,
    `bot_notify_to_group_name` VARCHAR(191) NOT NULL,
    `bot_notify_group_privilige` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `Chat` (
    `id` INTEGER NOT NULL AUTO_INCREMENT,
    `chatid` VARCHAR(191) NOT NULL,
    `senderId` INTEGER NULL,
    `type` VARCHAR(191) NOT NULL,
    `data` LONGTEXT NOT NULL,
    `createAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `idx_chat_chatid`(`chatid`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `Chat` ADD CONSTRAINT `fk_chat_customer_chatid` FOREIGN KEY (`chatid`) REFERENCES `Customer`(`chatid`) ON DELETE RESTRICT ON UPDATE CASCADE;
