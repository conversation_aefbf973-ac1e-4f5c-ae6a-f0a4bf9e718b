FROM --platform=linux/amd64 node:20.9.0-alpine AS base

ARG PORT=3000

WORKDIR /src

# Build
FROM base as build

COPY package*.json ./
RUN npm install

COPY . .
COPY ./prisma ./prisma

RUN npx prisma generate --schema=./prisma/schema.prisma
# # Verify Prisma client copy
# RUN ls -l /src/.output/node_modules/.prisma/client
RUN npx prisma validate
RUN npm run build

# Run
FROM base

ENV PORT=$PORT
ENV NODE_ENV=production

COPY --from=build /src/.output /src/.output

CMD [ "node", ".output/server/index.mjs" ]

EXPOSE 3000