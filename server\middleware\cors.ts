const runtimeConfig = useRuntimeConfig();

export default defineEventHandler(async (event) => {
  const headers = event.node.res.setHeader;
  const origin = [runtimeConfig.storeUrl, runtimeConfig.endpoint];

  headers["Access-Control-Allow-Origin"] = origin;
  // Allow specific HTTP methods
  (headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE"),
    "OPTIONS";
  // Allow specific headers
  headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization";
  // Allow credentials if needed
  headers["Access-Control-Allow-Credentials"] = "true";

  if (event.node.req.method === "OPTIONS") {
    event.node.res.statusCode = 200;
    event.node.res.end();
  }
});
