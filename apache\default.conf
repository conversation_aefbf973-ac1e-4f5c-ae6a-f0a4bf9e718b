# Listen on port 80
Listen 80

# Define the server name (default for localhost)
ServerName _

# Set the document root (equivalent to root directive in Nginx)
DocumentRoot "/usr/local/apache2/htdocs"

# Default index files
DirectoryIndex index.html index.htm

# Error page for 404 (equivalent to Nginx's error_page)
ErrorDocument 404 /200.html

# Directory settings
<Directory "/usr/local/apache2/htdocs">
    # Allow overrides, FollowSymLinks, and Indexes (this matches Nginx's default behavior)
    Options Indexes FollowSymLinks

    # Allow Apache to serve the directory
    AllowOverride All

    # Set permissions for all
    Require all granted

    # Equivalent to try_files in Nginx
    RewriteEngine On
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteRule ^(.+)$ /200.html [L]
</Directory>
