// Define the Product interface based on the expected response structure
interface Product {
  id: number;
  title: string;
  price: number;
  description: string;
  category: string;
  image: Array<string>;
}

export default defineEventHandler(async (event) => {
  const { id } = event.context.params as Record<string, string>; // Ensure params are typed

  // Check if ID is defined
  if (!id) {
    return {
      status: false,
      message: "Product ID is required",
    };
  }

  const config = useRuntimeConfig();
  const uri =
    config.public.apiUrl + config.public.apiPrefix + "/items/details/" + id; // Construct the URL

  try {
    const response = await $fetch(uri, {
      method: "GET",
      headers: {
        // moduleId: config.public.moduleId,
        moduleId: "1",
        zoneId: config.public.zoneId,
      },
    });

    return {
      status: true,
      message: "ok",
      data: response,
    };
  } catch (error) {
    console.error(error); // Log the error for debugging
    return {
      status: false,
      message: "External System Error",
      data: error instanceof Error ? error.message : String(error), // More informative error handling
    };
  }
});
