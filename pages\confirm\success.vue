<template>

  <div class="bg-gray-100 flex items-center justify-center">
    <div class="max-w-md w-full bg-white shadow-md rounded-lg p-6">
      <div class="icon-container text-green-500 mx-auto w-24 text-6xl mb-4">
        <video autoplay loop muted playsinline>
          <source src="https://cdn-icons-mp4.flaticon.com/512/18545/18545145.mp4" type="video/mp4" />
        </video>
      </div>
      <h2 class="text-2xl font-semibold text-center text-gray-800">
        Order Successful!
      </h2>
      <p class="text-gray-700 text-center mt-2">Your order ID is:</p>
      <div class="bg-gray-200 text-gray-800 text-center py-2 mt-2 rounded-md border">
        <!-- <span class="font-medium">#{{ order_id }}</span> -->
        <span class="font-medium">#{{ order_id }}</span>
      </div>

      <div class="receipt mt-4">
        <h3 class="font-semibold text-md text-gray-800">Delivery Location:</h3>
        <p class="text-gray-700">
          {{ address }}
        </p>
      </div>

      <div class="receipt mt-4">
        <h3 class="font-semibold text-md text-gray-800">Order Details:</h3>
        <ul class="mt-2 space-y-2 text-gray-700">
          <li class="flex justify-between">
            <span>{{ product }} x {{ quantity }}</span>
            <span>${{ price }}</span>
          </li>
        </ul>
      </div>

      <div class="receipt mt-4">
        <h3 class="font-semibold text-md text-gray-800">Payment:</h3>
        <ul class="mt-2 space-y-2 text-gray-700">
          <li class="flex justify-between">
            <span>Subtotal:</span>
            <span>${{ price * quantity }}</span>
          </li>
          <li class="flex justify-between">
            <span>Payment method:</span>
            <!-- <span class="badge badge-neutral items-center">{{ paymethod }}</span> -->
            <span class="badge badge-neutral items-center" style="color: #fff"
              v-if="paymethod === 'cash_on_delivery'">Cash on Delivery</span>
          </li>
          <li class="flex justify-between">
            <span>Discount</span>
            <span>${{ ((price * discount) / 100) }}</span>
          </li>
          <li class="flex justify-between">
            <span>VAT and Fee:</span>
            <span>$0.00</span>
          </li>

          <li class="flex justify-between">
            <span>Delivery Fee:</span>
            <span>${{ shipping }}</span>
          </li>
          <li class="flex justify-between font-semibold">
            <span>Grand Total:</span>
            <span>${{ total }}</span>

          </li>
        </ul>
      </div>

      <div class="mt-6">
        <nuxt-link href="/" class="btn btn-primary w-full">Done</nuxt-link>
      </div>
    </div>

    <!-- <h1 class="text-2xl font-bold"> Your order number is #{{ orderNumnber }} </h1> -->
  </div>
</template>

<script lang="ts" setup>
const orderNumnber = Math.floor(Math.random() * 1000000);
const route = useRoute();
definePageMeta({
  layout: "custom", // Disable layout for this page
});
const props = defineProps({
  product: { type: String, required: true },
  img: { type: String, required: true },
  price: { type: Number, required: true },
  discount: { type: Number, required: true },
  shipping: { type: Number, required: true },
  total: { type: Number, required: true },
  paymethod: { type: String },
  address: { type: String },
  order_id: { type: String },
  //chatid: {type: bigInt}
});

const product = route.query.product;
const img = route.query.img;
const price = Number(route.query.price);
const quantity = Number(route.query.quantity);
const discount = Number(route.query.discount);
const shipping = Number(route.query.shipping);
const order_id = Number(route.query.order_id);
const total = Number(route.query.total);
const paymethod = route.query.paymethod;
const address = route.query.address;
const defaultAddress = "Contact for address";
const chatid = route.query.chatid;

</script>
